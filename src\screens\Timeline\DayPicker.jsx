import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  IconButton,
  Typography,
  Box,
  Popper,
  Paper,
  Switch,
  FormControlLabel,
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import { PickersDay } from "@mui/x-date-pickers/PickersDay";
import dayjs from "dayjs";
import PropTypes from "prop-types";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import isoWeek from "dayjs/plugin/isoWeek";
import { styled } from "@mui/material/styles";

// Extend dayjs with necessary plugins
dayjs.extend(isoWeek);

// Custom styled day component
const CustomPickersDay = styled(PickersDay)(({ theme }) => ({
  borderRadius: '50%',
  '&.Mui-selected': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
  },
}));

/**
 * DayPicker Component
 * Allows users to pick a day or date range using a date calendar
 */
const DayPicker = ({ onChange, isRange = false, startDate, endDate }) => {
  // State to track the selected date(s)
  const [selectedDate, setSelectedDate] = useState(dayjs(startDate || new Date()));
  const [selectedEndDate, setSelectedEndDate] = useState(dayjs(endDate || new Date()));

  // State to track if we're selecting a range
  const [selectingRange, setSelectingRange] = useState(isRange);

  // State to track if we're selecting the start or end date
  const [selectingEndDate, setSelectingEndDate] = useState(false);

  // State to manage the visibility of the date picker
  const [openPicker, setOpenPicker] = useState(false);

  // Refs for detecting outside clicks
  const calendarRef = useRef(null);
  const calendarContainerRef = useRef(null);

  // Sync with external props - using refs to prevent infinite loops
  const initialPropsSet = useRef(false);
  const prevProps = useRef({ startDate: null, endDate: null });

  useEffect(() => {
    // Skip if we've already processed these exact props
    const currentStartDate = startDate ? dayjs(startDate).format('YYYY-MM-DD') : null;
    const currentEndDate = endDate ? dayjs(endDate).format('YYYY-MM-DD') : null;

    if (prevProps.current.startDate === currentStartDate &&
        prevProps.current.endDate === currentEndDate) {
      return;
    }

    // Update previous props
    prevProps.current = { startDate: currentStartDate, endDate: currentEndDate };

    // Only set dates from props on initial render or when props explicitly change
    if (!initialPropsSet.current ||
        (startDate && currentStartDate !== selectedDate.format('YYYY-MM-DD')) ||
        (endDate && currentEndDate !== selectedEndDate.format('YYYY-MM-DD'))) {

      console.log("Syncing with external props:", { startDate, endDate });

      if (startDate) {
        setSelectedDate(dayjs(startDate));
      }
      if (endDate) {
        setSelectedEndDate(dayjs(endDate));
      }

      // Update range selection state based on whether start and end dates are different
      if (startDate && endDate &&
          dayjs(startDate).format('YYYY-MM-DD') !== dayjs(endDate).format('YYYY-MM-DD')) {
        setSelectingRange(true);
      }

      initialPropsSet.current = true;
    }
  }, [startDate, endDate, selectedDate, selectedEndDate]);

  // Call onChange callback when dates change, but with a ref to prevent infinite loops
  const lastEmittedRange = useRef({ startDate: null, endDate: null });

  useEffect(() => {
    const formattedStart = selectedDate.format("YYYY-MM-DD");
    const formattedEnd = selectedEndDate.format("YYYY-MM-DD");

    // Only emit changes if the dates have actually changed
    if (lastEmittedRange.current.startDate !== formattedStart ||
        lastEmittedRange.current.endDate !== formattedEnd) {

      // Log the current date range for debugging
      console.log("DayPicker current date range:", {
        startDate: formattedStart,
        endDate: formattedEnd,
        isRange: formattedStart !== formattedEnd
      });

      // Update the last emitted range
      lastEmittedRange.current = { startDate: formattedStart, endDate: formattedEnd };

      // Call onChange with the current date range
      if (onChange) {
        console.log("DayPicker updating dates:", { startDate: formattedStart, endDate: formattedEnd });
        onChange({ startDate: formattedStart, endDate: formattedEnd });
      }
    }
  }, [selectedDate, selectedEndDate, onChange]);

  // Handle date selection when a new date is picked
  const handleDateChange = (date) => {
    // Skip if date is null or undefined
    if (!date) { return }

    console.log("Date selected:", date.format("YYYY-MM-DD"));

    // Create a stable date object to prevent unnecessary re-renders
    const stableDate = dayjs(date.format("YYYY-MM-DD"));

    if (selectingRange) {
      if (selectingEndDate) {
        // If selecting end date, ensure it's after or equal to start date
        if (stableDate.isBefore(selectedDate)) {
          // If selected date is before start date, swap them
          setSelectedEndDate(selectedDate);
          setSelectedDate(stableDate);
        } else {
          setSelectedEndDate(stableDate);
        }
        // Done selecting range
        setSelectingEndDate(false);
        setOpenPicker(false);
      } else {
        // Selecting start date
        setSelectedDate(stableDate);
        // Now select end date
        setSelectingEndDate(true);
      }
    } else {
      // Not selecting a range, just set both dates to the same day
      setSelectedDate(stableDate);
      setSelectedEndDate(stableDate);
      setOpenPicker(false);
    }
  };

  // Close the date picker when clicking outside
  const handleClickOutside = useCallback((event) => {
    if (
      calendarContainerRef.current &&
      !calendarContainerRef.current.contains(event.target) &&
      calendarRef.current &&
      !calendarRef.current.contains(event.target)
    ) {
      setOpenPicker(false);
    }
  }, []);

  useEffect(() => {
    if (openPicker) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openPicker, handleClickOutside]);

  // Handlers to navigate days
  const goToPreviousDay = () => {
    let newEndDate = null;
    let newStartDate = null;

    if (isRange && !selectedDate.isSame(selectedEndDate, 'day')) {
      // If it's a range, move the entire range back by the number of days in the range
      const diffDays = selectedEndDate.diff(selectedDate, 'day');
      newStartDate = selectedDate.subtract(diffDays, "day");
      newEndDate = selectedEndDate.subtract(diffDays, "day");
    } else {
      // For single day, just move back one day
      newStartDate = selectedDate.subtract(1, "day");
      newEndDate = selectedEndDate.subtract(1, "day");
    }

    // Update local state
    setSelectedDate(newStartDate);
    setSelectedEndDate(newEndDate);

    // Notify parent component of the change
    if (onChange) {
      onChange({
        startDate: newStartDate.format("YYYY-MM-DD"),
        endDate: newEndDate.format("YYYY-MM-DD")
      });
    }
  };

  const goToNextDay = () => {
    let newEndDate = null;
    let newStartDate = null;

    if (isRange && !selectedDate.isSame(selectedEndDate, 'day')) {
      // If it's a range, move the entire range forward by the number of days in the range
      const diffDays = selectedEndDate.diff(selectedDate, 'day');
      newStartDate = selectedDate.add(diffDays, "day");
      newEndDate = selectedEndDate.add(diffDays, "day");
    } else {
      // For single day, just move forward one day
      newStartDate = selectedDate.add(1, "day");
      newEndDate = selectedEndDate.add(1, "day");
    }

    // Update local state
    setSelectedDate(newStartDate);
    setSelectedEndDate(newEndDate);

    // Notify parent component of the change
    if (onChange) {
      onChange({
        startDate: newStartDate.format("YYYY-MM-DD"),
        endDate: newEndDate.format("YYYY-MM-DD")
      });
    }
  };

  // Format the date display text
  const getDateDisplayText = () => {
    // Check if start and end dates are different
    if (!selectedDate.isSame(selectedEndDate, 'day')) {
      // If dates are in the same month and year
      if (selectedDate.month() === selectedEndDate.month() &&
          selectedDate.year() === selectedEndDate.year()) {
        return `${selectedDate.format("MMM D")} - ${selectedEndDate.format("D, YYYY")}`;
      }
      // If dates are in different months but same year
      else if (selectedDate.year() === selectedEndDate.year()) {
        return `${selectedDate.format("MMM D")} - ${selectedEndDate.format("MMM D, YYYY")}`;
      }
      // If dates are in different years
      else {
        return `${selectedDate.format("MMM D, YYYY")} - ${selectedEndDate.format("MMM D, YYYY")}`;
      }
    } else {
      // Single day
      return selectedDate.format("dddd, MMMM D, YYYY");
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box display="flex" justifyContent="flex-end" alignItems="center" gap={1} position="relative">
        {/* Previous Day Button */}
        <IconButton onClick={goToPreviousDay}>
          <ChevronLeftIcon sx={{ color: "grey.500" }} />
        </IconButton>

        {/* Display Selected Date or Date Range */}
        <Typography
          variant="body1"
          sx={{
            color: "text.secondary",
            borderRadius: "4px",
            padding: "4px 8px",
          }}
        >
          {getDateDisplayText()}
        </Typography>

        {/* Calendar Icon Button */}
        <IconButton ref={calendarRef} onClick={() => setOpenPicker((prev) => !prev)}>
          <CalendarTodayIcon sx={{ color: "grey.500" }} />
        </IconButton>

        {/* Popper to display the Calendar */}
        <Popper
          ref={calendarContainerRef}
          open={openPicker}
          anchorEl={calendarRef.current}
          placement="bottom"
          disablePortal
          modifiers={[
            {
              name: "preventOverflow",
              options: { boundary: "window" },
            },
          ]}
        >
          <Paper elevation={3} sx={{ p: 1, position: "relative", right: "100px" }}>
            <Box sx={{ p: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={selectingRange}
                    onChange={(e) => {
                      setSelectingRange(e.target.checked);
                      setSelectingEndDate(false);
                    }}
                    color="primary"
                  />
                }
                label="Date Range"
              />

              {selectingRange && selectingEndDate && (
                <Typography variant="caption" color="primary" sx={{ textAlign: 'center' }}>
                  Select end date
                </Typography>
              )}

              {selectingRange && !selectingEndDate && (
                <Typography variant="caption" color="primary" sx={{ textAlign: 'center' }}>
                  Select start date
                </Typography>
              )}

              <DateCalendar
                value={selectingEndDate ? selectedEndDate : selectedDate}
                onChange={handleDateChange}
                onMonthChange={(month) => {
                  console.log("Month changed to:", month.format("YYYY-MM"));
                  // Don't update the selected date when just changing months
                  // This prevents the infinite loop
                }}
                showDaysOutsideCurrentMonth
                disableFuture={false}
                disablePast={false}
                views={['day', 'month', 'year']}
                openTo="day"
                slots={{
                  day: CustomPickersDay
                }}
                // Prevent auto-selection of today when changing months
                reduceAnimations
              />
            </Box>
          </Paper>
        </Popper>

        {/* Next Day Button */}
        <IconButton onClick={goToNextDay}>
          <ChevronRightIcon sx={{ color: "grey.500" }} />
        </IconButton>
      </Box>
    </LocalizationProvider>
  );
};

DayPicker.propTypes = {
  onChange: PropTypes.func,
  isRange: PropTypes.bool,
  startDate: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  endDate: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
};

export default DayPicker;
