import { createSlice } from "@reduxjs/toolkit";


export const ActivitySlice = createSlice({
    name: "Activity",
    initialState: {
        activityArr: [], // For single user activity (legacy)
        multiUserActivityArr: [], // For multi-user activity data (new)
    },
    reducers: {
        createTodayGoal: () => {
        },
        createTodayStatus: () => {
        },
        getUserActivitySuccessfull: (state,action) => {
            

            if(action.payload.length === 0) {
                state.activityArr = []
                state.multiUserActivityArr = []
              
            } else {
         
                const firstItem = action.payload[0];

                // More robust detection: check for multi-user specific properties
                const hasMultiUserProps = Object.prototype.hasOwnProperty.call(firstItem, 'name') &&
                    Object.prototype.hasOwnProperty.call(firstItem, 'email');
                const hasSingleUserProps = Object.prototype.hasOwnProperty.call(firstItem, 'user') &&
                    Object.prototype.hasOwnProperty.call(firstItem, 'checkInTime');
                const hasActivityViewProps = Object.prototype.hasOwnProperty.call(firstItem, 'clockin') ||
                    Object.prototype.hasOwnProperty.call(firstItem, 'weekData') ||
                    Object.prototype.hasOwnProperty.call(firstItem, 'worked');

                const isMultiUserData = hasMultiUserProps && !hasSingleUserProps && hasActivityViewProps;

          

                if (isMultiUserData) {
                    state.multiUserActivityArr = action.payload;
                
                } else {
                    state.activityArr = action.payload;
                    // console.log("🔍 ActivitySlice - REDUCER SINGLE-USER ACTIVITY LOG ", action.payload);
                }
            }

        },
        getUserActivity: () => {
        },
        checkOutStatusUpdate: () => {},
        breakStartRed: () => {},
        breakEndRed: () => {},
        lateCheckIn: () => {},
        earlyCheckOut: () => {},
        idelStartRed: () => {},
        idelEndRed: () => {},
        productivityStatusRed: () => {},
        overLimitBreakRed: () => {},
        eraseActivity: (state = []) => {
            state.activityArr = []
            state.multiUserActivityArr = []
        },
        createTimelineRequest: () => {},
        updateTimelineRequest: () => {},
        getTimelineRequests: () => {}
    }
});

export default ActivitySlice;