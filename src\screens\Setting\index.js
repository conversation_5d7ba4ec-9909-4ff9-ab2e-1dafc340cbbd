import React, {useEffect} from "react";
import {Box, Button, Card, FormControl, Grid, InputBase, Typography, useTheme} from "@mui/material";
import PageTitle from "../../components/PageTitle";
import Input from "../../components/Input";
import {useFormik} from "formik";
import {useDispatch, useSelector} from "react-redux";
import {GeneralActions, SettingActions} from "../../slices/actions";
import {SettingSelector} from "../../selectors/SettingSelector";
import {UserSelector} from "../../selectors/UserSelector";
import Can from "../../utils/can";
import {Autocomplete} from "@mui/lab";
import COUNTRIES from "../../constants/countries";
import {toast} from "react-toastify";
import {GeneralSelector} from "../../selectors";
import FormSkeleton from "../../components/Skeleton/FormSkeleton";

export default function Setting() {
    const dispatch = useDispatch();
    const theme = useTheme();
const setting = useSelector(state => state.setting.setting);
const loading = useSelector(state => state.setting.loading);
    const profile = useSelector(UserSelector.profile()) || {};
    const countries = COUNTRIES.map(item => ({
        id: item.id,
        name: item.name,
        phoneCode: item.phoneCode,
        flag: item.flag
    }));

    // Check if user has permission to update settings
    // This uses the Can utility function which checks for both admin role and specific permissions
    const canUpdateSettings = Can('update', 'Setting');

    // Debug user permissions
    console.log('User profile:', profile);
    console.log('Can update settings?', canUpdateSettings);

    const success = useSelector(GeneralSelector.success(SettingActions.updateSetting.type));
    const error = useSelector(GeneralSelector.error(SettingActions.updateSetting.type));

    useEffect(() => {
        dispatch(SettingActions.getSetting());
    }, []);

  useEffect(() => {
    if (success) {
        toast.success(`${success?.message ?? "Success"}`, {
            position: "top-right",
            autoClose: 3000,
            closeOnClick: true,
            pauseOnHover: false
        });

        dispatch(GeneralActions.removeSuccess(SettingActions.updateSetting.type));
-       dispatch(SettingActions.getSetting());
    }
}, [success]);


    // Handle errors from API calls
    useEffect(() => {
        if (error) {
            // Check if it's a permission error (403)
            if (error.message && error.message.includes('Forbidden')) {
                toast.error('You need permission to update settings', {
                    position: "top-right",
                    autoClose: 5000,
                    closeOnClick: true,
                    pauseOnHover: true
                });
            } else {
                toast.error(`${error.message || "Failed to update settings"}`, {
                    position: "top-right",
                    autoClose: 3000,
                    closeOnClick: true,
                    pauseOnHover: false
                });
            }

            dispatch(GeneralActions.removeError(SettingActions.updateSetting.type));
        }
    }, [error]);

const formik = useFormik({
    initialValues: {
        name: setting?.name ?? '',
        address: setting?.address ?? '',
        city: setting?.city ?? '',
        country: '',
        email: setting?.email ?? '',
        phone: setting?.phone ?? '',
        phoneCode: '', // ✅ add this
        phoneNumber: '', // ✅ and this
        leaveLimit: setting?.leaveLimit ?? 0,
        day: setting?.day ?? 0
    },
    enableReinitialize: true,
    onSubmit: (values) => {
        handleSubmit(values);
    }
});

    useEffect(() => {
        const code = formik.values.country?.phoneCode;
        const phone = formik.values.phone;

        formik.setFieldValue('phoneCode', code ?? '');
        formik.setFieldValue('phone', phone);
    }, [formik.values.country]);


useEffect(() => {
    if (!setting) { return }

    const phone = setting.phone || '';
    const country = countries.find(e => e.name === setting?.country);

    if (country && formik.values.country?.name !== country.name) {
        formik.setFieldValue('country', country);
    }

    if (phone && country) {
        const code = country.phoneCode || '';
        const currentCode = formik.values.phoneCode ?? '';
        const currentPhone = formik.values.phoneNumber ?? '';

        if (currentCode !== code || currentPhone !== phone.substring(code.length)) {
            formik.setFieldValue('phoneCode', code);
            formik.setFieldValue('phoneNumber', phone.substring(code.length));
        }
    }
}, [setting, countries]);



  const handleSubmit = (values) => {
    if (!setting || !setting._id) {
        toast.error('Settings not loaded yet. Please wait a moment and try again.');
        return;
    }

    if (!canUpdateSettings) {
        toast.error('You need permission to update settings');
        return;
    }

    const params = {
        id: setting._id,
        name: values.name,
        address: values.address,
        city: values.city,
        country: values.country?.name ?? '',
        email: values.email,
        phone: (values.phoneCode || '') + (values.phoneNumber || ''),
        leaveLimit: values.leaveLimit,
        day: values.day
    };

    dispatch(SettingActions.updateSetting(params));
};


    return (
        <Box>
            <PageTitle title='Company Setting'/>

            <Card>
                {/* Extract the error check to a variable for cleaner code */}
                {(() => {
                    const hasError = useSelector(state =>
                        state.general.errors.find(e => e.action === SettingActions.getSetting.type)
                    );

                    if (loading) {
                        return <FormSkeleton />;
                    }

                    if (hasError) {
                        return (
                            <Box sx={{
                                p: 3,
                                textAlign: 'center',
                                border: '1px solid #f0f0f0',
                                borderRadius: 1,
                                bgcolor: '#fff9f9',
                                my: 2
                            }}>
                                <Typography color="error" variant="h6" gutterBottom>
                                    Access Denied
                                </Typography>
                                <Typography variant="body1">
                                    You do not have permission to access settings
                                </Typography>
                                <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                                    Please contact your administrator if you believe you should have access.
                                </Typography>
                            </Box>
                        );
                    }

                    return (
                        <form onSubmit={formik.handleSubmit}>
                            {!canUpdateSettings && (
                                <Box sx={{
                                    p: 2,
                                    mb: 3,
                                    border: '1px solid #ffcc80',
                                    borderRadius: 1,
                                    bgcolor: '#fff8e1',
                                }}>
                                    <Typography color="warning.main" variant="subtitle1" fontWeight="medium">
                                        View-Only Mode
                                    </Typography>
                                    <Typography variant="body2">
                                        You need permission to update settings. You can view the settings but cannot make changes.
                                    </Typography>
                                </Box>
                            )}
                        <Grid container spacing={3}>
                            <Grid item lg={6} sm={12} xs={12}>
                                <Input
                                    label='Name'
                                    name='name'
                                    value={formik.values.name}
                                    onChange={formik.handleChange}
                                    error={formik.touched.name && Boolean(formik.errors.name)}
                                   helpertext={formik.touched.name ? formik.errors.name : ""}/>
                            </Grid>
                            <Grid item lg={6} sm={12} xs={12}>
                                <Input
                                    label='Email'
                                    name='email'
                                    value={formik.values.email}
                                    onChange={formik.handleChange}/>
                            </Grid>
                            <Grid item lg={6} sm={12} xs={12}>
                                <FormControl fullWidth>
                                    <Typography variant='caption'>Country</Typography>
                                    <Autocomplete
                                        disablePortal
                                        name='country'
                                        options={countries}
                                        value={formik.values.country}
                                        onChange={(e, val) => {
                                            formik.setFieldValue('country', val);
                                        }}
                                        getOptionLabel={(option) => option.name ?? ''}
                                        renderOption={(props, option) => (
                                            <Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>
                                                {option.flag} {option.name}
                                            </Box>
                                        )}
                                        renderInput={(params) => <InputBase {...params.InputProps} {...params} />}
                                    />
                                </FormControl>
                            </Grid>
                            <Grid item lg={6} sm={12} xs={12}>
                                <FormControl fullWidth>
                                    <Typography variant='caption'>Phone Number</Typography>
                                    <Box sx={{
                                        display: 'flex',
                                        gap: 1.5
                                    }}>
                                        <Box sx={{ width: 100 }}>
                                            <Input
                                                sx={{
                                                    textAlign: 'center',
                                                    '& .Mui-disabled': {
                                                        fillColor: theme.palette.common.black
                                                    }
                                                }}
                                                autoComplete='new-password'
                                                name='phoneCode'
                                                startAdornment='+'
                                                type='number'
                                                value={formik.values.phoneCode}
                                                onChange={formik.handleChange}/>
                                        </Box>
                                        <Input
                                            name='phoneNumber'
                                            value={formik.values.phoneNumber}
                                            onChange={formik.handleChange}/>
                                    </Box>
                                </FormControl>
                            </Grid>
                            <Grid item lg={6} sm={12} xs={12}>
                                <Input
                                    label="City"
                                    name='city'
                                    value={formik.values.city}
                                    onChange={formik.handleChange}/>
                            </Grid>
                            <Grid item lg={6} sm={12} xs={12}>
                                <Input
                                    label="Address"
                                    name='address'
                                    value={formik.values.address}
                                    onChange={formik.handleChange}/>
                            </Grid>
                            <Grid item lg={6} sm={12} xs={12}>
                                <Input
                                    label="Day"
                                    name='day'
                                    value={formik.values.day}
                                    onChange={formik.handleChange}/>
                            </Grid>
                            <Grid item lg={12} sm={12} xs={12}>
                                <Typography variant='h6'>HR Setting</Typography>
                            </Grid>
                            <Grid item lg={6} xs={12}>
                                <Input
                                    type='number'
                                    label="Leave Quote"
                                    name='leaveLimit'
                                    value={formik.values.leaveLimit}
                                    onChange={formik.handleChange}/>
                            </Grid>
                            <Grid item lg={12} container justifyContent='flex-end'>
                             <Button
    type='submit'
    variant='contained'
    color='primary'
    disabled={!canUpdateSettings || !setting?._id}
    title={!canUpdateSettings ? 'You need permission to update settings' : 'Save settings'}
>
    {canUpdateSettings ? 'Submit' : 'No Permission'}
</Button>

                            </Grid>
                        </Grid>
                    </form>
                    );
                })()}
            </Card>
        </Box>
    )
}