// socketService.js
import { io } from "socket.io-client";

class SocketService {
  socket = null;

  connect(token) {
    if (!this.socket) {
      this.socket = io("http://localhost:10000", {
        transports: ["websocket"],
        auth: { token } // Send token with connection
      });

      console.log("Socket connected with token");
    }
    return this.socket;
  }

  on(event, callback) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  emit(event, data) {
    if (this.socket) {
      this.socket.emit(event, data);
    }
  }

  off(event, callback) {
    if (this.socket) {

      this.socket.off(event, callback);
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      console.log("Socket disconnected");
    }
  }
}

export default new SocketService();
