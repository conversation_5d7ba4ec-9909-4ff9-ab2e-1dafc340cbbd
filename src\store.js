/**
 * Redux Store Configuration
 *
 * This module sets up the Redux store with middleware for:
 * - Redux Toolkit for simplified Redux usage
 * - Redux Saga for handling side effects
 * - Connected React Router for synchronizing router state with Redux
 */

import { combineReducers, configureStore } from "@reduxjs/toolkit";
import createSagaMiddleware from 'redux-saga';
import { createBrowserHistory } from "history";
import { connectRouter, routerMiddleware } from "connected-react-router";
import rootSaga from "./sagas";
import reducers from "./slices/reducers";

/**
 * Browser history instance for routing
 * Exported for use in other parts of the application
 */
export const history = createBrowserHistory();

/**
 * Router middleware for syncing router state with Redux
 */
const connectedRouterMiddleware = routerMiddleware(history);

/**
 * Creates the root reducer by combining all slice reducers with router state
 *
 * @param {Object} history - Browser history instance
 * @returns {Function} Combined root reducer
 */
const createRootReducer = (history) => combineReducers({
    router: connectRouter(history),
    ...reducers
});

/**
 * Creates and configures the Redux store
 *
 * @returns {Object} Configured Redux store
 */
const store = () => {
    // Create saga middleware for handling side effects
    const sagaMiddleware = createSagaMiddleware();

    // Configure store with reducers and middleware
    const store = configureStore({
        reducer: createRootReducer(history),
        middleware: [sagaMiddleware, connectedRouterMiddleware],
    });

    // Run the root saga
    sagaMiddleware.run(rootSaga);

    return store;
};

export default store();
