import React, { useState, useEffect } from "react";
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  Checkbox,
  FormControlLabel,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Chip,
  Divider
} from "@mui/material";
import { useParams } from "react-router-dom";
import { features } from "constants/permission";
import { getApiUrl } from "utils/apiConfig";


// Define available actions
const actionLabels = {
  read: "Can View",
  read_all: "Can View All",
  read_some: "Can View Some",
  read_self: "Can View Own",
  create: "Can Create",
  update: "Can Edit",
  delete: "Can Delete",
};

// Define feature categories for better organization
const featureCategories = {
  core: {
    title: "Core Features",
    features: ["Dashboard", "User", "Department", "Designation", "Setting"]
  },
  attendance: {
    title: "Attendance & Leave",
    features: ["Attendance", "Leave", "Calendar", "Configuration", "Approve", "Leave Report"]
  },
  projects: {
    title: "Projects & Tasks",
    features: ["Projects", "Project List", "Project Overview", "Project Timesheet","Sprint", "Client", "Tasks", "My Tasks", "Task Note"]
  },
  timeline: {
    title: "Timeline & Scheduling",
    features: ["Timeline", "Overview", "Time Request", "Task Request", "Work Schedule"]
  },
  expenses: {
    title: "Finance",
    features: ["Expense", "Report"]
  }
};

// Define menu information with routes and access type
const menuInfo = {
  // Core Features
  Dashboard: {
    menuName: "Dashboard",
    route: "/app/dashboard",
    access: "both admin and user",
    description: "View dashboard with activity and statistics",
    category: "core"
  },
  User: {
    menuName: "Employee Management",
    route: "/app/user",
    access: "admin only",
    description: "Manage all employees in the system",
    category: "core"
  },
  Department: {
    menuName: "Department",
    route: "/app/department",
    access: "admin only",
    description: "Manage company departments",
    category: "core"
  },
  Designation: {
    menuName: "Designation",
    route: "/app/designation",
    access: "admin only",
    description: "Manage job designations",
    category: "core"
  },
  Setting: {
    menuName: "Setting",
    route: "/app/setting",
    access: "both admin and user",
    description: "Manage system settings - accessible to both admins and users with proper permissions",
    category: "core"
  },

  // Attendance & Leave
  Attendance: {
    menuName: "Attendance",
    route: "/app/attendance",
    access: "admin only",
    description: "View and manage attendance for all employees",
    category: "attendance"
  },
  Leave: {
    menuName: "Leave Management",
    route: "/app/leave",
    access: "admin only",
    description: "Manage leave requests for all employees",
    category: "attendance"
  },
  Calendar: {
    menuName: "Calendar",
    route: "/app/leave/calendar",
    access: "admin only",
    description: "View leave calendar",
    category: "attendance",
    parent: "Leave"
  },
  Configuration: {
    menuName: "Configuration",
    route: "/app/leave/configuration",
    access: "admin only",
    description: "Configure leave settings",
    category: "attendance",
    parent: "Leave"
  },
  Approve: {
    menuName: "Approval",
    route: "/app/leave/approval",
    access: "admin only",
    description: "Approve leave requests",
    category: "attendance",
    parent: "Leave"
  },
  "Leave Report": {
    menuName: "Leave Report",
    route: "/app/leave/report",
    access: "admin only",
    description: "View leave reports",
    category: "attendance",
    parent: "Leave"
  },

  // Projects & Tasks
  Projects: {
    menuName: "Projects",
    route: "/app/project/list",
    access: "admin only",
    description: "Manage all projects",
    category: "projects"
  },
  "Project List": {
    menuName: "Project List",
    route: "/app/project/list",
    access: "admin only",
    description: "View list of all projects",
    category: "projects",
    parent: "Projects"
  },
  "Project Overview": {
    menuName: "Project Overview",
    route: "/app/project/overview",
    access: "admin only",
    description: "View project overview",
    category: "projects",
    parent: "Projects"
  },
  "Project Timesheet": {
    menuName: "Project Timesheet",
    route: "/app/project/timesheet",
    access: "admin only",
    description: "Manage project timesheets",
    category: "projects",
    parent: "Projects"
  },
  Client: {
    menuName: "Client",
    route: "/app/client",
    access: "admin only",
    description: "Manage clients",
    category: "projects"
  },
  Tasks: {
    menuName: "Tasks",
    route: "/app/tasks",
    access: "both admin and user",
    description: "View and manage tasks",
    category: "projects"
  },
  "My Tasks": {
    menuName: "My Tasks",
    route: "/app/tasks",
    access: "user only",
    description: "View and manage your tasks",
    category: "projects"
  },
  "Task Note": {
    menuName: "Task Note",
    route: "/app/user/tasklist/note",
    access: "user only",
    description: "View task notes",
    category: "projects",
    parent: "My Tasks"
  },
  // Add to menuInfo object
Sprint: {
  menuName: "Sprints",
  route: "/app/sprint",
  access: "both admin and user",  // Changed from "admin only"
  description: "View all sprints with read_all, or own sprints with read_self",
  category: "projects"
},

"usersprint": {
  menuName: "My Sprints",
  route: "/app/user/sprint",
  access: "user only",
  description: "View and manage your created and assigned sprints",
  category: "projects",
  adminEquivalent: "Sprint"
},



  // Timeline & Scheduling
  Timeline: {
    menuName: "Timeline",
    route: "/app/timeline",
    access: "admin only",
    description: "View and manage timelines",
    category: "timeline"
  },
  Overview: {
    menuName: "Overview",
    route: "/app/timeline/overview",
    access: "admin only",
    description: "View timeline overview",
    category: "timeline",
    parent: "Timeline"
  },
  "Time Request": {
    menuName: "Time Request",
    route: "/app/timeline/request",
    access: "admin only",
    description: "Manage time requests",
    category: "timeline",
    parent: "Timeline"
  },
  "Task Request": {
    menuName: "Task Request",
    route: "/app/timeline/taskrequest",
    access: "admin only",
    description: "Manage task requests",
    category: "timeline",
    parent: "Timeline"
  },
  "Work Schedule": {
    menuName: "Work Schedule",
    route: "/app/timeline/workschedule",
    access: "admin only",
    description: "Manage work schedules",
    category: "timeline",
    parent: "Timeline"
  },

  // Finance
  Expense: {
    menuName: "Expenses",
    route: "/app/expenses",
    access: "admin only",
    description: "Manage all expense reports",
    category: "expenses"
  },
  Report: {
    menuName: "Report",
    route: "/app/report",
    access: "admin only",
    description: "View and generate reports",
    category: "expenses"
  },

  // User-specific versions of admin features
  projectoverview: {
    menuName: "Project Overview",
    route: "/app/user/project/overview",
    access: "user only",
    description: "View your project overview",
    category: "projects",
    adminEquivalent: "Project Overview"
  },
  projecttimesheet: {
    menuName: "Project Timesheet",
    route: "/app/user/project/timesheet",
    access: "user only",
    description: "Manage your project timesheets",
    category: "projects",
    adminEquivalent: "Project Timesheet"
  },
  projectlist: {
    menuName: "Project List",
    route: "/app/user/projects",
    access: "user only",
    description: "View your projects",
    category: "projects",
    adminEquivalent: "Project List"
  }
};

// Helper function to determine which actions to show for each feature
const getAvailableActionsForFeature = (featureKey, featureLabel) => {
  // Default actions for all features
  const defaultActions = ['read', 'create', 'update', 'delete'];

  // Features that need specialized read permissions
const needsReadAll = [
  'Dashboard', 'Attendance', 'Leave', 'Projects', 'Project List',
  'Project Overview', 'Project Timesheet', 'Timeline', 'Overview',
  'Time Request', 'Task Request', 'Work Schedule', 'Client', 'Sprint'
];


  const needsReadSome = [
    'Attendance', 'Leave', 'User'
  ];

 const needsReadSelf = [
  'Attendance', 'Leave', 'Tasks', 'My Tasks', 'usersprint', 'Sprint'
];


  // Start with default actions
  let actions = [...defaultActions];

  // Add specialized read permissions if needed
  if (needsReadAll.includes(featureLabel)) {
    actions.push('read_all');
  }

  if (needsReadSome.includes(featureLabel)) {
    actions.push('read_some');
  }

  if (needsReadSelf.includes(featureLabel)) {
    actions.push('read_self');
  }

  return actions;
};

export default function Permission({ user }) {
  // Get userId either from props or from URL params
  const { userId: urlUserId } = useParams();
  const userId = user?._id || urlUserId;

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Store permissions in format: { feature: [actions] }
  const [permissions, setPermissions] = useState({});

  // Debug user and userId
  useEffect(() => {
    console.log('Permission component - User prop:', user);
    console.log('Permission component - User ID from prop:', user?._id);
    console.log('Permission component - URL userId:', urlUserId);
    console.log('Permission component - Using userId:', userId);

    // Check token
    const token = localStorage.getItem('merakihr-token');
    console.log('Token available:', token ? 'Yes' : 'No');

    if (!userId) {
      console.warn('No userId available from either props or URL');
    }
  }, [user, urlUserId, userId]);

  // Fetch user permissions when component mounts
  useEffect(() => {
    if (userId) {
      console.log('Fetching permissions for user ID:', userId);
      fetchUserPermissions();
    } else {
      console.warn('No userId provided to Permission component');
      setError('User ID is missing. Cannot fetch permissions.');
    }
  }, [userId]);

  // Fetch user permissions from the backend
  const fetchUserPermissions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use fetch instead of axios as per project preference
      const token = localStorage.getItem('merakihr-token');

      // Debug token
      console.log('Token for permissions request:', token ? 'Token exists' : 'No token found');

      // Check if we're in admin mode or user mode
      const isAdminView = window.location.pathname.includes('/app/user/');
      console.log('Is admin view:', isAdminView);

      // Initialize with empty array as default
      let permissionsData = [];

      if (isAdminView) {
        // Admin view - use permissions API
        // Use the API config utility to get the correct URL
        const apiUrl = getApiUrl(`permissions/${userId}`);
        console.log('Fetching permissions from admin API:', apiUrl);

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          // If admin API fails (likely due to permissions), try the user API
          throw new Error('Admin permissions API access denied');
        }

        permissionsData = await response.json();
      } else {
        // User view - use user profile API which doesn't require admin access
        const apiUrl = getApiUrl(`user/${userId}`);
        // console.log('Fetching permissions from user API:', apiUrl);

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }

        const userData = await response.json();
        permissionsData = userData.permissions || [];
      }

      // Process the permissions data
      // console.log('User Permissions (Raw):', permissionsData);

      // Convert backend format to component format
      const formattedPermissions = {};
      if (Array.isArray(permissionsData)) {
        permissionsData.forEach(permission => {
          // Handle special cases for feature naming consistency
          let normalizedFeat = permission.feat;

          // Find the feature key in the features object
          const featureEntry = Object.entries(features).find(([_, val]) => val === permission.feat);
          if (featureEntry) {
            // Use the key from features object
            normalizedFeat = featureEntry[0];
          } else {
            // Handle special cases
            if (permission.feat === 'My Tasks') {
              normalizedFeat = 'mytasks';
            } else if (permission.feat === 'Project List') {
              normalizedFeat = 'projectlist';
            } else if (permission.feat === 'Project Overview') {
              normalizedFeat = 'projectoverview';
            } else if (permission.feat === 'Project Timesheet') {
              normalizedFeat = 'projecttimesheet';
            } else {
              // Convert to lowercase with no spaces for consistency
              normalizedFeat = permission.feat.toLowerCase().replace(/\s+/g, '');
            }
          }

          formattedPermissions[normalizedFeat] = permission.acts;
        });
      } else {
        console.warn('Permissions data is not an array:', permissionsData);
      }

      // Log the formatted permissions
      console.log('User Permissions (Formatted):', formattedPermissions);

      setPermissions(formattedPermissions);
    } catch (err) {
      setError(err.message || 'Failed to load permissions');
      console.error('Error fetching permissions:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle permission toggle
  const handleToggle = (feature, action) => {
    setPermissions((prev) => {
      const current = prev[feature] || [];
      const updated = current.includes(action) ? current.filter((a) => a !== action) : [...current, action];

      // Log the permission change
      console.log(`Permission toggle: ${feature} - ${action} - ${current.includes(action) ? 'REMOVED' : 'ADDED'}`);
      console.log(`Feature ${feature} now has permissions: ${updated.join(', ') || 'EMPTY'}`);

      return { ...prev, [feature]: updated };
    });
  };

  // Save permissions to the backend
  const handleSave = async () => {
    try {
      // Validate userId before proceeding
      if (!userId) {
        setError('Cannot save permissions: User ID is missing');
        console.error('Attempted to save permissions without a user ID');
        return;
      }

      setSaving(true);
      setError(null);
      setSuccess(false);

      // Convert component format to backend format
      const formattedPermissions = Object.entries(permissions).map(([feat, acts]) => {
        // Handle special cases for feature naming consistency
        let normalizedFeat = feat;

        // Special case for tasks - use "My Tasks" in the database
        if (feat === 'tasks' || feat === 'mytasks') {
          normalizedFeat = 'My Tasks';
        }

        // Special case for project features - ensure consistent casing
        if (feat === 'projectlist' || feat === 'projectList') {
          normalizedFeat = 'Project List';
        }

        if (feat === 'projectoverview' || feat === 'projectOverview') {
          normalizedFeat = 'Project Overview';
        }

        if (feat === 'projecttimesheet' || feat === 'projectTimesheet') {
          normalizedFeat = 'Project Timesheet';
        }

        // For features in the features object, use the exact string from there
        const featureEntry = Object.entries(features).find(([key, _]) => key === feat);
        if (featureEntry) {
          normalizedFeat = featureEntry[1];
        }

        return {
          feat: normalizedFeat,
          acts
        };
      });

      // Log the permissions being saved
      console.log('Saving User Permissions:', {
        userId,
        permissions: formattedPermissions
      });

      // Use fetch instead of axios as per project preference
      const token = localStorage.getItem('merakihr-token');

      // Debug the request payload
      console.log('Request payload:', JSON.stringify({ permissions: formattedPermissions }));

      // Check if we're in admin mode or user mode
      const isAdminView = window.location.pathname.includes('/app/user/');
      console.log('Is admin view for saving:', isAdminView);

      // Initialize with null as default
      let response = null;

      if (isAdminView) {
        // Admin view - use permissions API
        // Use the API config utility to get the correct URL
        const apiUrl = getApiUrl(`permissions/${userId}`);
        console.log('Saving permissions to admin API:', apiUrl);

        response = await fetch(apiUrl, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ permissions: formattedPermissions })
        });
      } else {
        // User view - use user update API which doesn't require admin access
        const apiUrl = getApiUrl(`user/${userId}`);
        console.log('Saving permissions to user API:', apiUrl);

        response = await fetch(apiUrl, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ permissions: formattedPermissions })
        });
      }

      // Clone the response so we can read it multiple times if needed
      const clonedResponse = response.clone();

      // Handle response based on status
      if (!response.ok) {
        // Try to get error message from response
        let errorMessage = 'Failed to update permissions';
        try {
          const errorData = await clonedResponse.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.log('Error response is not valid JSON:', e);
          // Only try to get text if the first attempt failed
          try {
            const errorText = await response.text();
            if (errorText) {
              errorMessage = errorText;
            }
          } catch (textError) {
            console.error('Could not parse error response:', textError);
          }
        }
        throw new Error(errorMessage);
      }

      // For successful responses, use a default message in case parsing fails
      let responseData = { message: 'Permissions updated successfully' };
      try {
        // Use the original response for successful responses
        responseData = await response.json();
        console.log('Success response:', responseData);
      } catch (e) {
        console.log('Success response is not valid JSON, using default message');
        // responseData already initialized with default value
      }

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);

      return responseData;
    } catch (err) {
      setError(err.message || 'Failed to save permissions');
      console.error('Error saving permissions:', err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" sx={{ mb: 2 }}>
        User Permissions
      </Typography>

      {/* Show loading state */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Show error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Show success message */}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Permissions updated successfully!
        </Alert>
      )}

      {/* Permissions grid */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: 'primary.main' }}>
          PERMISSION SYSTEM REFERENCE GUIDE
        </Typography>
        <Typography variant="body2" sx={{ mb: 3 }}>
          This guide shows which permissions correspond to which menus and routes in the system.
          When you grant a permission, the user will be able to access the corresponding menu and route.
        </Typography>

        <Divider sx={{ mb: 3 }} />

        {/* Render permissions by category */}
        {Object.entries(featureCategories).map(([categoryKey, category]) => (
          <Box key={categoryKey} sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: 'text.primary' }}>
              {category.title}
            </Typography>

            {/* Render features in this category */}
            {category.features.map(featureLabel => {
              // Get the feature key from features object
              const featureKey = Object.entries(features).find(([key, val]) => val === featureLabel)?.[0] || featureLabel.toLowerCase().replace(/\s+/g, '');
              const info = menuInfo[featureLabel] || {};

              // Skip child features - they'll be shown with their parents
              if (info.parent) {
                return null;
              }

              const isAdminOnly = info.access === 'admin only';
              const isUserOnly = info.access === 'user only';
              const isBoth = info.access === 'both admin and user';

              // Determine background color and chip color based on access type
              let bgColor = 'white';
              let chipColor = 'default';

              if (isAdminOnly) {
                bgColor = '#f8f9fa';
                chipColor = 'error';
              } else if (isUserOnly) {
                bgColor = '#f0f4ff';
                chipColor = 'primary';
              } else if (isBoth) {
                bgColor = '#e6f7ff'; // Light blue for both
                chipColor = 'success';
              }

              // Find child features
              const childFeatures = Object.entries(menuInfo).filter(([_, childInfo]) =>
                childInfo.parent === featureLabel
              ).map(([childKey, _]) => childKey);

              return (
                <Box key={featureKey} sx={{
                  mb: 2,
                  p: 2,
                  border: '1px solid #eee',
                  borderRadius: 1,
                  backgroundColor: bgColor
                }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      {featureLabel}
                    </Typography>

                    {info.access && (
                      <Chip
                        label={info.access}
                        size="small"
                        color={chipColor}
                        sx={{
                          fontWeight: 'bold',
                          textTransform: 'uppercase',
                          fontSize: '0.7rem'
                        }}
                      />
                    )}
                  </Box>

                  {info.menuName && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', display: 'inline' }}>
                        Menu:
                      </Typography>
                      <Typography variant="body2" sx={{ display: 'inline' }}>
                        {info.menuName}
                      </Typography>
                    </Box>
                  )}

                  {info.route && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', display: 'inline' }}>
                        Route:
                      </Typography>
                      <Typography variant="body2" sx={{ display: 'inline', fontFamily: 'monospace' }}>
                        {info.route}
                      </Typography>
                    </Box>
                  )}

                  {info.description && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        {info.description}
                      </Typography>
                    </Box>
                  )}

                  <Divider sx={{ my: 1 }} />

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {/* Only show relevant actions for this feature */}
                    {Object.entries(actionLabels).filter(([action]) =>
                        getAvailableActionsForFeature(featureKey, featureLabel).includes(action)).map(([action, label]) => (
                        <FormControlLabel
                          key={action}
                          control={
                            <Checkbox
                              checked={permissions[featureKey]?.includes(action) || false}
                              onChange={() => handleToggle(featureKey, action)}
                              disabled={saving}
                            />
                          }
                          label={label}
                        />
                    ))}
                  </Box>

                  {/* Render child features if any */}
                  {childFeatures.length > 0 && (
                    <Box sx={{ mt: 2, ml: 3, borderLeft: '2px solid #eee', pl: 2 }}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                        Child Features:
                      </Typography>

                      {childFeatures.map(childFeatureLabel => {
                        const childFeatureKey = Object.entries(features).find(([key, val]) => val === childFeatureLabel)?.[0] || childFeatureLabel.toLowerCase().replace(/\s+/g, '');
                        const childInfo = menuInfo[childFeatureLabel] || {};

                        return (
                          <Box key={childFeatureKey} sx={{
                            mb: 2,
                            p: 2,
                            border: '1px solid #eee',
                            borderRadius: 1,
                            backgroundColor: 'rgba(0,0,0,0.02)'
                          }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                                {childFeatureLabel}
                              </Typography>

                              {childInfo.access && (
                                <Chip
                                  label={childInfo.access}
                                  size="small"
                                  color={chipColor}
                                  sx={{
                                    fontWeight: 'bold',
                                    textTransform: 'uppercase',
                                    fontSize: '0.7rem'
                                  }}
                                />
                              )}
                            </Box>

                            {childInfo.route && (
                              <Box sx={{ mb: 1 }}>
                                <Typography variant="body2" sx={{ fontWeight: 'bold', display: 'inline' }}>
                                  Route:
                                </Typography>
                                <Typography variant="body2" sx={{ display: 'inline', fontFamily: 'monospace' }}>
                                  {childInfo.route}
                                </Typography>
                              </Box>
                            )}

                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                              {/* Only show relevant actions for this child feature */}
                              {Object.entries(actionLabels).filter(([action]) =>
                                  getAvailableActionsForFeature(childFeatureKey, childFeatureLabel).includes(action)).map(([action, label]) => (
                                  <FormControlLabel
                                    key={action}
                                    control={
                                      <Checkbox
                                        checked={permissions[childFeatureKey]?.includes(action) || false}
                                        onChange={() => handleToggle(childFeatureKey, action)}
                                        disabled={saving}
                                        size="small"
                                      />
                                    }
                                    label={label}
                                  />
                              ))}
                            </Box>
                          </Box>
                        );
                      })}
                    </Box>
                  )}
                </Box>
              );
            })}
          </Box>
        ))}
      </Box>

      {/* Save button */}
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSave}
          disabled={saving || loading}
        >
          {saving ? 'Saving...' : 'Save Permissions'}
        </Button>
      </Box>
    </Card>
  );
}

// PropTypes validation
Permission.propTypes = {
  user: PropTypes.shape({
    _id: PropTypes.string,
    name: PropTypes.string,
    email: PropTypes.string,
    permissions: PropTypes.array
  }),
  form: PropTypes.object,
  setForm: PropTypes.func
};

// Default props
Permission.defaultProps = {
  user: null
};
