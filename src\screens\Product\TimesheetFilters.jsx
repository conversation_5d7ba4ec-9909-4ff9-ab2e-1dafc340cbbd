import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Box, FormControl, Select, MenuItem, Button } from "@mui/material";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { UserSelector } from "selectors";
import { UserActions } from "slices/actions";
import { useSelector, useDispatch } from "react-redux";

const TimesheetFilters = ({ onFilter, projects }) => {
  const statuses = ["Ongoing", "Completed", "On Hold"];
  const priorities = ["Low", "Medium", "High", "Critical"];
  const [filters, setFilters] = useState({
    project: "", // Changed from team to project
    user: "",
    status: "",
    type: "",
    tag: "",
  });

  const dispatch = useDispatch();

  const users = useSelector(UserSelector.getUsers()) || [];

  const getUsernameById = (userId) => {
    const user = users.find((u) => u._id === userId);
    return user ? user.name : "Unknown User";
  };

  useEffect(() => {
    dispatch(UserActions.getUsers()); // Ensure user list is fetched
  }, [dispatch]);

  const handleChange = (event) => {
    const { name, value } = event.target;
    setFilters((prevFilters) => ({ ...prevFilters, [name]: value }));
  };

  const handleFilter = () => {
    console.log("Applying Filters:", filters);
    onFilter(filters);
  };

  const handleReset = () => {
    const resetFilters = {
      project: "",
      user: "",
      status: "",
      type: "",
      tag: "",
    };
    setFilters(resetFilters);
    onFilter(resetFilters);
  };

  return (
    <Box display="flex" alignItems="center" gap={2} sx={{ flexWrap: "wrap" }}>
      {/* Project Dropdown */}
      <FormControl
        size="small"
        sx={{ width: 140, borderRadius: 2, boxShadow: 1 }}
      >
        <Select
          name="project"
          value={filters.project}
          onChange={handleChange}
          displayEmpty
          renderValue={(selected) =>
            (selected? projects.find((p) => p._id === selected)?.productName ||
                "Unknown": "Project")
          }
        >
          <MenuItem value=""> Project</MenuItem>
          {projects?.map((project) => (
            <MenuItem
              key={project._id}
              value={project._id}
              sx={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {project.productName}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* User Dropdown */}
      <FormControl
        size="small"
        sx={{ width: 140, borderRadius: 2, boxShadow: 1 }}
      >
        <Select
          name="user"
          value={filters.user}
          onChange={handleChange}
          displayEmpty
          renderValue={(selected) =>
            (selected ? getUsernameById(selected) : "User")
          }
        >
          <MenuItem value=""> User</MenuItem>
          {Array.from(
            new Set(projects.flatMap((project) => project.members))
          ).map((userId) => (
            <MenuItem
              key={userId}
              value={userId}
              sx={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {getUsernameById(userId)}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Status Dropdown */}
      <FormControl
        size="small"
        sx={{ width: 140, borderRadius: 2, boxShadow: 1 }}
      >
        <Select
          name="status"
          value={filters.status}
          onChange={handleChange}
          displayEmpty
          renderValue={(selected) => (selected ? selected : "Status")}
        >
          <MenuItem value=""> Status</MenuItem>
          {statuses.map((status, index) => (
            <MenuItem key={index} value={status}>
              {status}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Tyoe Dropdown */}
      <FormControl
        size="small"
        sx={{ width: 140, borderRadius: 2, boxShadow: 1 }}
      >
        <Select
          name="type"
          value={filters.type}
          onChange={handleChange}
          displayEmpty
          renderValue={(selected) => (selected ? selected : "Type")}
        >
          <MenuItem value=""> Type</MenuItem>
          <MenuItem value="bug">Bug</MenuItem>
          <MenuItem value="feature">Feature</MenuItem>
        </Select>
      </FormControl>

      {/* Tag Dropdown */}
      <FormControl
        size="small"
        sx={{ width: 140, borderRadius: 2, boxShadow: 1 }}
      >
        <Select
          name="tag"
          value={filters.tag}
          onChange={handleChange}
          displayEmpty
          renderValue={(selected) => (selected ? selected : "Tag")}
        >
          <MenuItem value=""> Tag</MenuItem>
          {priorities.map((priority, index) => (
            <MenuItem key={index} value={priority}>
              {priority}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Filter Button */}
      <Button
        variant="contained"
        sx={{
          backgroundColor: "#6200ea",
          color: "white",
          borderRadius: 2,
          textTransform: "none",
          boxShadow: 2,
          "&:hover": { backgroundColor: "#4b00c0" },
        }}
        onClick={handleFilter}
        startIcon={<FilterAltIcon />}
      >
        Filter
      </Button>

      {/* Reset Button */}
      <Button
        variant="contained"
        sx={{
          backgroundColor: "#d32f2f",
          color: "white",
          borderRadius: 2,
          textTransform: "none",
          boxShadow: 2,
          "&:hover": { backgroundColor: "#b71c1c" },
        }}
        onClick={handleReset}
        startIcon={<RestartAltIcon />}
      >
        Reset
      </Button>
    </Box>
  );
};

TimesheetFilters.propTypes = {
  onFilter: PropTypes.func.isRequired,
  projects: PropTypes.array.isRequired,
};

export default TimesheetFilters;
