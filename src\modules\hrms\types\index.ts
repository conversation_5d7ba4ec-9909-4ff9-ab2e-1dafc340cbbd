/**
 * HRMS Types Index
 * 
 * This file exports all TypeScript type definitions for the Human Resource Management System module.
 * Following strict typing conventions for better code quality and IDE support.
 * 
 * Usage:
 * import { Leave, Attendance, LeaveType } from 'modules/hrms/types';
 */

// Base Types
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

// Leave Types
export interface Leave extends BaseEntity {
  employeeId: string;
  leaveTypeId: string;
  startDate: string;
  endDate: string;
  totalDays: number;
  reason?: string;
  status: LeaveStatus;
  appliedDate: string;
  approvedBy?: string;
  approvedDate?: string;
  rejectionReason?: string;
  isHalfDay?: boolean;
  halfDayType?: HalfDayType;
  attachments?: string[];
  comments?: LeaveComment[];
}

export enum LeaveStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  WITHDRAWN = 'withdrawn'
}

export enum HalfDayType {
  FIRST_HALF = 'first_half',
  SECOND_HALF = 'second_half'
}

export interface LeaveComment extends BaseEntity {
  leaveId: string;
  userId: string;
  comment: string;
  type: CommentType;
}

export enum CommentType {
  GENERAL = 'general',
  APPROVAL = 'approval',
  REJECTION = 'rejection',
  SYSTEM = 'system'
}

// Leave Type Configuration
export interface LeaveType extends BaseEntity {
  name: string;
  code: string;
  description?: string;
  color: string;
  isPaid: boolean;
  isCarryForward: boolean;
  maxCarryForward?: number;
  isEncashable: boolean;
  maxEncashment?: number;
  requiresApproval: boolean;
  approvalLevels: number;
  minAdvanceNotice: number; // days
  maxConsecutiveDays?: number;
  isActive: boolean;
  applicableGenders?: Gender[];
  applicableEmployeeTypes?: EmployeeType[];
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other'
}

export enum EmployeeType {
  PERMANENT = 'permanent',
  CONTRACT = 'contract',
  INTERN = 'intern',
  CONSULTANT = 'consultant'
}

// Leave Policy
export interface LeavePolicy extends BaseEntity {
  name: string;
  description?: string;
  leaveTypeId: string;
  entitlementRules: EntitlementRule[];
  accrualRules: AccrualRule[];
  isActive: boolean;
}

export interface EntitlementRule {
  employeeType: EmployeeType;
  yearsOfService: number;
  entitlementDays: number;
  probationPeriodDays?: number;
}

export interface AccrualRule {
  frequency: AccrualFrequency;
  accrualRate: number; // days per frequency
  maxAccrual?: number;
}

export enum AccrualFrequency {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly'
}

// Attendance Types
export interface Attendance extends BaseEntity {
  employeeId: string;
  date: string;
  checkInTime?: string;
  checkOutTime?: string;
  totalHours?: number;
  status: AttendanceStatus;
  workLocation?: WorkLocation;
  notes?: string;
  approvedBy?: string;
  approvedDate?: string;
  isManualEntry: boolean;
  breaks?: AttendanceBreak[];
}

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  HALF_DAY = 'half_day',
  ON_LEAVE = 'on_leave',
  HOLIDAY = 'holiday',
  WEEKEND = 'weekend'
}

export enum WorkLocation {
  OFFICE = 'office',
  HOME = 'home',
  CLIENT_SITE = 'client_site',
  OTHER = 'other'
}

export interface AttendanceBreak {
  startTime: string;
  endTime: string;
  duration: number; // minutes
  type: BreakType;
}

export enum BreakType {
  LUNCH = 'lunch',
  TEA = 'tea',
  PERSONAL = 'personal',
  MEETING = 'meeting'
}

// Leave Balance
export interface LeaveBalance extends BaseEntity {
  employeeId: string;
  leaveTypeId: string;
  year: number;
  entitlement: number;
  used: number;
  pending: number;
  available: number;
  carryForward: number;
  encashed: number;
  lapsed: number;
}

// Holiday Configuration
export interface Holiday extends BaseEntity {
  name: string;
  date: string;
  type: HolidayType;
  isOptional: boolean;
  description?: string;
  applicableLocations?: string[];
  applicableDepartments?: string[];
}

export enum HolidayType {
  NATIONAL = 'national',
  REGIONAL = 'regional',
  RELIGIOUS = 'religious',
  COMPANY = 'company'
}

// HR Dashboard Types
export interface HRMetrics {
  totalEmployees: number;
  presentToday: number;
  onLeaveToday: number;
  absentToday: number;
  pendingLeaveRequests: number;
  upcomingLeaves: number;
  attendanceRate: number;
  leaveUtilization: number;
}

export interface LeaveMetrics {
  totalRequests: number;
  approvedRequests: number;
  pendingRequests: number;
  rejectedRequests: number;
  averageProcessingTime: number; // days
  mostUsedLeaveType: string;
}

export interface AttendanceMetrics {
  averageWorkingHours: number;
  onTimePercentage: number;
  lateArrivalPercentage: number;
  earlyDeparturePercentage: number;
  overtimeHours: number;
}

// Filter Types
export interface LeaveFilters {
  status?: LeaveStatus[];
  leaveTypeId?: string;
  employeeId?: string;
  departmentId?: string;
  dateRange?: DateRange;
  search?: string;
}

export interface AttendanceFilters {
  status?: AttendanceStatus[];
  employeeId?: string;
  departmentId?: string;
  workLocation?: WorkLocation;
  dateRange?: DateRange;
  search?: string;
}

export interface DateRange {
  startDate: string;
  endDate: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LeaveFormData {
  leaveTypeId: string;
  startDate: string;
  endDate: string;
  reason?: string;
  isHalfDay?: boolean;
  halfDayType?: HalfDayType;
  attachments?: File[];
}

export interface AttendanceFormData {
  date: string;
  checkInTime?: string;
  checkOutTime?: string;
  workLocation?: WorkLocation;
  notes?: string;
}

export interface LeaveApprovalFormData {
  leaveId: string;
  status: LeaveStatus;
  comments?: string;
  rejectionReason?: string;
}

// Notification Types
export interface HRNotification extends BaseEntity {
  type: NotificationType;
  title: string;
  message: string;
  recipientId: string;
  entityId?: string;
  entityType?: NotificationEntityType;
  isRead: boolean;
  readAt?: string;
  actionUrl?: string;
}

export enum NotificationType {
  LEAVE_REQUEST = 'leave_request',
  LEAVE_APPROVAL = 'leave_approval',
  LEAVE_REJECTION = 'leave_rejection',
  ATTENDANCE_REMINDER = 'attendance_reminder',
  POLICY_UPDATE = 'policy_update',
  HOLIDAY_ANNOUNCEMENT = 'holiday_announcement'
}

export enum NotificationEntityType {
  LEAVE = 'leave',
  ATTENDANCE = 'attendance',
  POLICY = 'policy',
  HOLIDAY = 'holiday'
}

// TODO: Add more specific types as needed during migration
// TODO: Implement proper validation schemas using Yup or Zod
// TODO: Add JSDoc comments for better documentation
// TODO: Consider using branded types for IDs to prevent mixing different entity IDs
// TODO: Add proper error types for better error handling
