import React, { useState } from "react";
import {
  <PERSON>u,
  <PERSON>u<PERSON><PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Box,
  Typography,
} from "@mui/material";
import { DateRange } from "@mui/icons-material";
import { DateRangePicker } from "@mui/x-date-pickers-pro/DateRangePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs from "dayjs";
import PropTypes from "prop-types";

const DateRangeSelector = ({ onRangeChange }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [customDialogOpen, setCustomDialogOpen] = useState(false);
  
  const [customRange, setCustomRange] = useState([dayjs().subtract(6, "day"), dayjs()]);
  
  const [selectedRange, setSelectedRange] = useState({
    label: "Last 7 Days",
    startDate: dayjs().subtract(6, "day"),
    endDate: dayjs(),
  });

  const open = Boolean(anchorEl);
  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  
  const formatDate = (date) => (date ? dayjs(date).format("MMM DD, YYYY") : "Invalid Date");

  const handleSelect = (label, startDate, endDate) => {
    if (!startDate || !endDate) { return; }
    const range = { label, startDate, endDate };
    setSelectedRange(range);
    handleClose();
    if (onRangeChange) { onRangeChange(range); }
  };

  const handleCustomApply = () => {
    if (!customRange[0] || !customRange[1]) { return; }
    setCustomDialogOpen(false);
    handleSelect("Custom Range", customRange[0], customRange[1]);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      {/* Modern Button for Date Range Selection */}
      <Button
        onClick={handleClick}
        startIcon={<DateRange />}
        variant="outlined"
        sx={{
          textTransform: "none",
          fontSize: "14px",
          borderRadius: "8px",
          borderColor: "primary.main",
          padding: "8px 16px",
          color: "primary.main",
          "&:hover": { borderColor: "primary.dark", backgroundColor: "action.hover" },
        }}
      >
        {selectedRange.label === "Custom Range"? `${formatDate(selectedRange.startDate)} - ${formatDate(selectedRange.endDate)}`: selectedRange.label}
      </Button>

      {/* Dropdown Menu for Preset Ranges */}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{ "& .MuiPaper-root": { borderRadius: "10px", minWidth: 180 } }}
      >
        <MenuItem onClick={() => handleSelect("Today", dayjs(), dayjs())}>Today</MenuItem>
        <MenuItem onClick={() => handleSelect("Yesterday", dayjs().subtract(1, "day"), dayjs().subtract(1, "day"))}>
          Yesterday
        </MenuItem>
        <MenuItem onClick={() => handleSelect("Last 7 Days", dayjs().subtract(6, "day"), dayjs())}>
          Last 7 Days
        </MenuItem>
        <MenuItem onClick={() => { setCustomDialogOpen(true); handleClose(); }}>
          Custom Range
        </MenuItem>
      </Menu>

      {/* Modern Custom Range Dialog */}
      <Dialog
        open={customDialogOpen}
        onClose={() => setCustomDialogOpen(false)}
        sx={{
          "& .MuiDialog-paper": { borderRadius: "12px", padding: "16px" },
        }}
      >
        <DialogContent sx={{ textAlign: "center", pb: 2 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: "bold" }}>Select Date Range</Typography>
          
          {/* Modernized Date Range Picker */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              borderRadius: "8px",
              border: "1px solid",
              borderColor: "divider",
              padding: "8px",
            }}
          >
            <DateRangePicker
              value={customRange}
              onChange={(newValue) =>
                setCustomRange([
                  newValue[0] || dayjs().subtract(6, "day"),
                  newValue[1] || dayjs(),
                ])
              }
              calendars={2}
              sx={{
                "& .MuiInputBase-root": {
                  borderRadius: "8px",
                  padding: "8px",
                  backgroundColor: "background.paper",
                },
                "& .MuiPickersCalendarHeader-label": { fontSize: "14px", fontWeight: "bold" },
                "& .MuiPickersDay-root": {
                  borderRadius: "50%",
                  "&:hover": { backgroundColor: "primary.light" },
                },
              }}
            />
          </Box>
        </DialogContent>

        {/* Dialog Actions */}
        <DialogActions sx={{ display: "flex", justifyContent: "space-between", padding: "12px" }}>
          <Button
            onClick={() => setCustomDialogOpen(false)}
            sx={{ color: "gray", fontSize: "14px" }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCustomApply}
            sx={{
              backgroundColor: "primary.main",
              color: "white",
              fontSize: "14px",
              "&:hover": { backgroundColor: "primary.dark" },
            }}
          >
            Apply
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

DateRangeSelector.propTypes = {
  onRangeChange: PropTypes.func,
};

export default DateRangeSelector;
