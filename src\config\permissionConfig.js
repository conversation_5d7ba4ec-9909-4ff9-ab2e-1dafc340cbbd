/**
 * Permission Configuration
 * 
 * This file serves as the single source of truth for all permission requirements
 * in the application. It defines what permissions are required for each route
 * and menu item.
 * 
 * The structure is:
 * {
 *   featureName: {
 *     name: "Display Name",
 *     description: "Feature description",
 *     routes: {
 *       routeKey: {
 *         path: "/app/route-path",
 *         permission: { feat: "FeatureName", act: "action" }
 *       }
 *     },
 *     menus: {
 *       menuKey: {
 *         name: "Menu Display Name",
 *         icon: IconComponent,
 *         path: "/app/route-path",
 *         permission: { feat: "FeatureName", act: "action" }
 *       }
 *     }
 *   }
 * }
 */

import { actions, features } from "../constants/permission";
import {
  Dashboard,
  People,
  AccountBalance,
  AccountTree,
  CalendarToday,
  EventNote,
  Task,
  AccountBalanceWallet,
  AssignmentIndOutlined,
  Assignment,
  Settings,
  MoreTime
} from "@mui/icons-material";

const permissionConfig = {
  // ===== DASHBOARD =====
  dashboard: {
    name: "Dashboard",
    description: "View and manage dashboards",
    routes: {
      main: {
        path: "/app/dashboard",
        permission: null // No permission required - redirects based on role
      },
      admin: {
        path: "/app/admin-dashboard",
        permission: { feat: features.dashboard, act: actions.readAll }
      },
      user: {
        path: "/app/user-dashboard",
        permission: { feat: features.dashboard, act: actions.read }
      }
    },
    menus: {
      admin: {
        name: "Admin Dashboard",
        icon: Dashboard,
        path: "/app/admin-dashboard",
        permission: { feat: features.dashboard, act: actions.readAll }
      },
      user: {
        name: "My Dashboard",
        icon: Dashboard,
        path: "/app/user-dashboard",
        permission: { feat: features.dashboard, act: actions.read }
      }
    }
  },

  // ===== USER MANAGEMENT =====
  user: {
    name: "Employee Management",
    description: "Manage employees and their information",
    routes: {
      list: {
        path: "/app/user",
        permission: { feat: features.user, act: actions.read }
      },
      create: {
        path: "/app/user/create",
        permission: { feat: features.user, act: actions.create }
      },
      update: {
        path: "/app/user/update/:id",
        permission: { feat: features.user, act: actions.update }
      },
      permissions: {
        path: "/app/user/permission/:id",
        permission: { feat: features.user, act: actions.update }
      }
    },
    menus: {
      main: {
        name: "Employee Management",
        icon: People,
        path: "/app/user",
        permission: { feat: features.user, act: actions.read }
      }
    }
  },

  // ===== DEPARTMENT =====
  department: {
    name: "Department",
    description: "Manage company departments",
    routes: {
      list: {
        path: "/app/department",
        permission: { feat: features.department, act: actions.read }
      },
      create: {
        path: "/app/department/create",
        permission: { feat: features.department, act: actions.create }
      },
      update: {
        path: "/app/department/update/:id",
        permission: { feat: features.department, act: actions.update }
      }
    },
    menus: {
      main: {
        name: "Department",
        icon: AccountBalance,
        path: "/app/department",
        permission: { feat: features.department, act: actions.read }
      }
    }
  },

  // ===== DESIGNATION =====
  designation: {
    name: "Designation",
    description: "Manage job designations",
    routes: {
      list: {
        path: "/app/designation",
        permission: { feat: features.designation, act: actions.read }
      },
      create: {
        path: "/app/designation/create",
        permission: { feat: features.designation, act: actions.create }
      },
      update: {
        path: "/app/designation/update/:id",
        permission: { feat: features.designation, act: actions.update }
      }
    },
    menus: {
      main: {
        name: "Designation",
        icon: AccountTree,
        path: "/app/designation",
        permission: { feat: features.designation, act: actions.read }
      }
    }
  },

  // ===== ATTENDANCE =====
  attendance: {
    name: "Attendance",
    description: "Manage employee attendance",
    routes: {
      list: {
        path: "/app/attendance",
        permission: { feat: features.attendance, act: actions.read }
      },
      create: {
        path: "/app/attendance/create",
        permission: { feat: features.attendance, act: actions.create }
      },
      update: {
        path: "/app/attendance/update/:id",
        permission: { feat: features.attendance, act: actions.update }
      }
    },
    menus: {
      admin: {
        name: "Attendance",
        icon: CalendarToday,
        path: "/app/attendance",
        permission: { feat: features.attendance, act: actions.readAll }
      },
      user: {
        name: "My Attendance",
        icon: CalendarToday,
        path: "/app/attendance",
        permission: { feat: features.attendance, act: actions.read }
      }
    }
  },

  // ===== EXPENSE =====
  expense: {
    name: "Expenses",
    description: "Manage expense reports",
    routes: {
      list: {
        path: "/app/expenses",
        permission: { feat: features.expense, act: actions.read }
      },
      create: {
        path: "/app/expenses/create",
        permission: { feat: features.expense, act: actions.create }
      },
      update: {
        path: "/app/expenses/update/:id",
        permission: { feat: features.expense, act: actions.update }
      },
      userList: {
        path: "/app/user/expenses",
        permission: { feat: features.expense, act: actions.read }
      }
    },
    menus: {
      admin: {
        name: "Expenses",
        icon: AccountBalanceWallet,
        path: "/app/expenses",
        permission: { feat: features.expense, act: actions.read }
      },
      user: {
        name: "My Expenses",
        icon: AccountBalanceWallet,
        path: "/app/expenses",
        permission: { feat: features.expense, act: actions.read }
      }
    }
  },

  // ===== LEAVE =====
  leave: {
    name: "Leave Management",
    description: "Manage employee leave requests",
    routes: {
      list: {
        path: "/app/leave",
        permission: { feat: features.leave, act: actions.readAll }
      },
      userList: {
        path: "/app/user/leave",
        permission: { feat: features.leave, act: actions.read }
      },
      create: {
        path: "/app/leave/create",
        permission: { feat: features.leave, act: actions.create }
      },
      update: {
        path: "/app/leave/update/:id",
        permission: { feat: features.leave, act: actions.update }
      },
      report: {
        path: "/app/leave/report",
        permission: { feat: features.leavereport, act: actions.readAll }
      },
      approval: {
        path: "/app/leave/approval",
        permission: { feat: features.approve, act: actions.readAll }
      },
      calendar: {
        path: "/app/leave/calendar",
        permission: { feat: features.calendar, act: actions.readAll }
      },
      configuration: {
        path: "/app/leave/configuration",
        permission: { feat: features.configuration, act: actions.readAll }
      }
    },
    menus: {
      admin: {
        name: "Leave Management",
        icon: AssignmentIndOutlined,
        path: "/app/leave",
        permission: { feat: features.leave, act: actions.readAll },
        children: [
          {
            name: "Leave Report",
            path: "/app/leave/report",
            permission: { feat: features.leavereport, act: actions.readAll }
          },
          {
            name: "Approval",
            path: "/app/leave/approval",
            permission: { feat: features.approve, act: actions.readAll }
          },
          {
            name: "Calendar",
            path: "/app/leave/calendar",
            permission: { feat: features.calendar, act: actions.readAll }
          },
          {
            name: "Configuration",
            path: "/app/leave/configuration",
            permission: { feat: features.configuration, act: actions.readAll }
          }
        ]
      },
      user: {
        name: "My Leave",
        icon: AssignmentIndOutlined,
        path: "/app/user/leave",
        permission: { feat: features.leave, act: actions.read }
      }
    }
  },

  // ===== PROJECTS =====
  projects: {
    name: "Projects",
    description: "Manage projects and project-related activities",
    routes: {
      list: {
        path: "/app/project/list",
        permission: { feat: features.projectlist, act: actions.readAll }
      },
      overview: {
        path: "/app/project/overview",
        permission: { feat: features.projectoverview, act: actions.readAll }
      },
      timesheet: {
        path: "/app/project/timesheet",
        permission: { feat: features.projecttimesheet, act: actions.readAll }
      },
      userList: {
        path: "/app/user/projects",
        permission: { feat: features.projects, act: actions.read }
      },
      userOverview: {
        path: "/app/user/project/overview",
        permission: { feat: features.projectoverview, act: actions.read }
      },
      userTimesheet: {
        path: "/app/user/project/timesheet",
        permission: { feat: features.projecttimesheet, act: actions.read }
      }
    },
    menus: {
      admin: {
        name: "Projects",
        icon: Task,
        path: "/app/project/list",
        permission: { feat: features.projectlist, act: actions.readAll },
        children: [
          {
            name: "Project Overview",
            path: "/app/project/overview",
            permission: { feat: features.projectoverview, act: actions.readAll }
          },
          {
            name: "Project List",
            path: "/app/project/list",
            permission: { feat: features.projectlist, act: actions.readAll }
          },
          {
            name: "Project Timesheet",
            path: "/app/project/timesheet",
            permission: { feat: features.projecttimesheet, act: actions.readAll }
          }
        ]
      },
      user: {
        name: "My Projects",
        icon: EventNote,
        path: "/app/user/projects",
        permission: { feat: features.projects, act: actions.read },
        children: [
          {
            name: "Project Overview",
            path: "/app/user/project/overview",
            permission: { feat: features.projectoverview, act: actions.read }
          },
          {
            name: "Project Timesheet",
            path: "/app/user/project/timesheet",
            permission: { feat: features.projecttimesheet, act: actions.read }
          }
        ]
      }
    }
  },

  // ===== TASKS =====
  tasks: {
    name: "Tasks",
    description: "Manage tasks and task-related activities",
    routes: {
      list: {
        path: "/app/tasks",
        permission: { feat: features.mytasks, act: actions.read }
      },
      note: {
        path: "/app/user/tasklist/note/:data",
        permission: { feat: features.tasknote, act: actions.read }
      }
    },
    menus: {
      user: {
        name: "My Tasks",
        icon: Task,
        path: "/app/tasks",
        permission: { feat: features.mytasks, act: actions.read }
      }
    }
  },

  // ===== TIMELINE =====
  timeline: {
    name: "Timeline",
    description: "Manage timelines and scheduling",
    routes: {
      main: {
        path: "/app/timeline",
        permission: { feat: features.timeline, act: actions.readAll }
      },
      overview: {
        path: "/app/timeline/overview",
        permission: { feat: features.overview, act: actions.readAll }
      },
      timeRequest: {
        path: "/app/timeline/request",
        permission: { feat: features.timerequest, act: actions.readAll }
      },
      taskRequest: {
        path: "/app/timeline/taskrequest",
        permission: { feat: features.taskrequest, act: actions.readAll }
      },
      workSchedule: {
        path: "/app/timeline/workschedule",
        permission: { feat: features.workschedule, act: actions.readAll }
      },
      userTimeline: {
        path: "/app/user/timeline",
        permission: { feat: features.timeline, act: actions.read }
      }
    },
    menus: {
      admin: {
        name: "Timeline",
        icon: MoreTime,
        path: "/app/timeline",
        permission: { feat: features.timeline, act: actions.readAll },
        children: [
          {
            name: "Overview",
            path: "/app/timeline/overview",
            permission: { feat: features.overview, act: actions.readAll }
          },
          {
            name: "Time Request",
            path: "/app/timeline/request",
            permission: { feat: features.timerequest, act: actions.readAll }
          },
          {
            name: "Task Request",
            path: "/app/timeline/taskrequest",
            permission: { feat: features.taskrequest, act: actions.readAll }
          },
          {
            name: "Work Schedule",
            path: "/app/timeline/workschedule",
            permission: { feat: features.workschedule, act: actions.readAll }
          }
        ]
      },
      user: {
        name: "My Timeline",
        icon: MoreTime,
        path: "/app/user/timeline",
        permission: { feat: features.timeline, act: actions.read }
      }
    }
  },

  // ===== CLIENT =====
  client: {
    name: "Client",
    description: "Manage clients",
    routes: {
      list: {
        path: "/app/client",
        permission: { feat: features.client, act: actions.readAll }
      }
    },
    menus: {
      main: {
        name: "Client",
        icon: AccountBalance,
        path: "/app/client",
        permission: { feat: features.client, act: actions.readAll }
      }
    }
  },

  // ===== REPORT =====
  report: {
    name: "Report",
    description: "View and generate reports",
    routes: {
      main: {
        path: "/app/report",
        permission: { feat: features.report, act: actions.read }
      }
    },
    menus: {
      main: {
        name: "Report",
        icon: Assignment,
        path: "/app/report",
        permission: { feat: features.report, act: actions.read }
      }
    }
  },

  // ===== SETTING =====
  setting: {
    name: "Setting",
    description: "Manage system settings",
    routes: {
      main: {
        path: "/app/setting",
        permission: { feat: features.setting, act: actions.read }
      }
    },
    menus: {
      main: {
        name: "Setting",
        icon: Settings,
        path: "/app/setting",
        permission: { feat: features.setting, act: actions.read }
      }
    }
  },

  // ===== PROFILE =====
  profile: {
    name: "Profile",
    description: "View and manage user profile",
    routes: {
      main: {
        path: "/app/profile",
        permission: null // No permission required
      }
    },
    menus: {} // No menu item for profile
  }
};

export default permissionConfig;
