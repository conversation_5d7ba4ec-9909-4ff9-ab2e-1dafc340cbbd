/**
 * HRMS Constants Index
 * 
 * This file exports all constants specific to the Human Resource Management System module.
 * These constants include API endpoints, default values, and configuration options.
 * 
 * Usage:
 * import { HRMS_ROUTES, LEAVE_STATUS_OPTIONS } from 'modules/hrms/constants';
 */

// API Routes
export const HRMS_ROUTES = {
  LEAVES: '/api/leaves',
  LEAVE_TYPES: '/api/leave-types',
  LEAVE_POLICIES: '/api/leave-policies',
  LEAVE_BALANCES: '/api/leave-balances',
  ATTENDANCE: '/api/attendance',
  HOLIDAYS: '/api/holidays',
  HR_REPORTS: '/api/hr-reports',
  LEAVE_APPROVALS: '/api/leave-approvals',
} as const;

// Leave Status Constants
export const LEAVE_STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending', color: '#FFC107' },
  { value: 'approved', label: 'Approved', color: '#28A745' },
  { value: 'rejected', label: 'Rejected', color: '#DC3545' },
  { value: 'cancelled', label: 'Cancelled', color: '#6C757D' },
  { value: 'withdrawn', label: 'Withdrawn', color: '#17A2B8' },
] as const;

// Attendance Status Constants
export const ATTENDANCE_STATUS_OPTIONS = [
  { value: 'present', label: 'Present', color: '#28A745' },
  { value: 'absent', label: 'Absent', color: '#DC3545' },
  { value: 'late', label: 'Late', color: '#FFC107' },
  { value: 'half_day', label: 'Half Day', color: '#17A2B8' },
  { value: 'on_leave', label: 'On Leave', color: '#6F42C1' },
  { value: 'holiday', label: 'Holiday', color: '#FD7E14' },
  { value: 'weekend', label: 'Weekend', color: '#6C757D' },
] as const;

// Work Location Constants
export const WORK_LOCATION_OPTIONS = [
  { value: 'office', label: 'Office', icon: 'business' },
  { value: 'home', label: 'Work from Home', icon: 'home' },
  { value: 'client_site', label: 'Client Site', icon: 'location_on' },
  { value: 'other', label: 'Other', icon: 'place' },
] as const;

// Half Day Type Constants
export const HALF_DAY_TYPE_OPTIONS = [
  { value: 'first_half', label: 'First Half (Morning)' },
  { value: 'second_half', label: 'Second Half (Afternoon)' },
] as const;

// Gender Constants
export const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
] as const;

// Employee Type Constants
export const EMPLOYEE_TYPE_OPTIONS = [
  { value: 'permanent', label: 'Permanent' },
  { value: 'contract', label: 'Contract' },
  { value: 'intern', label: 'Intern' },
  { value: 'consultant', label: 'Consultant' },
] as const;

// Holiday Type Constants
export const HOLIDAY_TYPE_OPTIONS = [
  { value: 'national', label: 'National Holiday', color: '#DC3545' },
  { value: 'regional', label: 'Regional Holiday', color: '#FFC107' },
  { value: 'religious', label: 'Religious Holiday', color: '#6F42C1' },
  { value: 'company', label: 'Company Holiday', color: '#007BFF' },
] as const;

// Accrual Frequency Constants
export const ACCRUAL_FREQUENCY_OPTIONS = [
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
  { value: 'yearly', label: 'Yearly' },
] as const;

// Break Type Constants
export const BREAK_TYPE_OPTIONS = [
  { value: 'lunch', label: 'Lunch Break', color: '#28A745' },
  { value: 'tea', label: 'Tea Break', color: '#FFC107' },
  { value: 'personal', label: 'Personal Break', color: '#17A2B8' },
  { value: 'meeting', label: 'Meeting Break', color: '#6F42C1' },
] as const;

// Notification Type Constants
export const NOTIFICATION_TYPE_OPTIONS = [
  { value: 'leave_request', label: 'Leave Request', icon: 'event_note' },
  { value: 'leave_approval', label: 'Leave Approval', icon: 'check_circle' },
  { value: 'leave_rejection', label: 'Leave Rejection', icon: 'cancel' },
  { value: 'attendance_reminder', label: 'Attendance Reminder', icon: 'schedule' },
  { value: 'policy_update', label: 'Policy Update', icon: 'policy' },
  { value: 'holiday_announcement', label: 'Holiday Announcement', icon: 'celebration' },
] as const;

// Default Values
export const DEFAULT_LEAVE_VALUES = {
  status: 'pending',
  isHalfDay: false,
  attachments: [],
  comments: [],
} as const;

export const DEFAULT_ATTENDANCE_VALUES = {
  status: 'present',
  workLocation: 'office',
  isManualEntry: false,
  breaks: [],
} as const;

export const DEFAULT_LEAVE_TYPE_VALUES = {
  isPaid: true,
  isCarryForward: false,
  isEncashable: false,
  requiresApproval: true,
  approvalLevels: 1,
  minAdvanceNotice: 1,
  isActive: true,
} as const;

// Working Hours Configuration
export const WORKING_HOURS_CONFIG = {
  STANDARD_HOURS_PER_DAY: 8,
  STANDARD_HOURS_PER_WEEK: 40,
  OVERTIME_THRESHOLD: 8,
  LATE_ARRIVAL_THRESHOLD: 15, // minutes
  EARLY_DEPARTURE_THRESHOLD: 15, // minutes
  LUNCH_BREAK_DURATION: 60, // minutes
  TEA_BREAK_DURATION: 15, // minutes
} as const;

// Leave Configuration
export const LEAVE_CONFIG = {
  MAX_ADVANCE_DAYS: 365,
  MIN_ADVANCE_DAYS: 1,
  MAX_CONSECUTIVE_DAYS: 30,
  MAX_CARRY_FORWARD_DAYS: 30,
  MAX_ENCASHMENT_DAYS: 15,
  PROBATION_PERIOD_DAYS: 90,
} as const;

// Pagination Constants
export const HRMS_PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const;

// Validation Constants
export const VALIDATION_RULES = {
  LEAVE_REASON_MIN_LENGTH: 10,
  LEAVE_REASON_MAX_LENGTH: 500,
  LEAVE_TYPE_NAME_MIN_LENGTH: 3,
  LEAVE_TYPE_NAME_MAX_LENGTH: 50,
  LEAVE_TYPE_CODE_MIN_LENGTH: 2,
  LEAVE_TYPE_CODE_MAX_LENGTH: 10,
  HOLIDAY_NAME_MIN_LENGTH: 3,
  HOLIDAY_NAME_MAX_LENGTH: 100,
  COMMENT_MIN_LENGTH: 5,
  COMMENT_MAX_LENGTH: 1000,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_FILE_TYPES: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
} as const;

// Chart Colors for HR Analytics
export const HR_CHART_COLORS = {
  APPROVED: '#28A745',
  PENDING: '#FFC107',
  REJECTED: '#DC3545',
  PRESENT: '#28A745',
  ABSENT: '#DC3545',
  LATE: '#FFC107',
  ON_LEAVE: '#6F42C1',
  PRIMARY: '#007BFF',
  SECONDARY: '#6C757D',
  SUCCESS: '#28A745',
  WARNING: '#FFC107',
  DANGER: '#DC3545',
  INFO: '#17A2B8',
} as const;

// Date Formats
export const HR_DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  INPUT: 'YYYY-MM-DD',
  DATETIME: 'MMM DD, YYYY HH:mm',
  TIME: 'HH:mm',
  MONTH_YEAR: 'MMM YYYY',
  DAY_MONTH: 'DD MMM',
} as const;

// Time Formats
export const TIME_FORMATS = {
  DISPLAY: 'HH:mm',
  INPUT: 'HH:mm',
  FULL: 'HH:mm:ss',
  AMPM: 'hh:mm A',
} as const;

// Local Storage Keys
export const HR_STORAGE_KEYS = {
  LEAVE_FILTERS: 'hr_leave_filters',
  ATTENDANCE_FILTERS: 'hr_attendance_filters',
  HR_PREFERENCES: 'hr_preferences',
  RECENT_LEAVES: 'hr_recent_leaves',
  DASHBOARD_SETTINGS: 'hr_dashboard_settings',
} as const;

// Email Templates
export const EMAIL_TEMPLATES = {
  LEAVE_REQUEST: 'leave_request_notification',
  LEAVE_APPROVAL: 'leave_approval_notification',
  LEAVE_REJECTION: 'leave_rejection_notification',
  ATTENDANCE_REMINDER: 'attendance_reminder',
  POLICY_UPDATE: 'policy_update_notification',
  HOLIDAY_ANNOUNCEMENT: 'holiday_announcement',
} as const;

// Report Types
export const REPORT_TYPES = {
  LEAVE_SUMMARY: 'leave_summary',
  ATTENDANCE_SUMMARY: 'attendance_summary',
  LEAVE_BALANCE: 'leave_balance',
  EMPLOYEE_ATTENDANCE: 'employee_attendance',
  DEPARTMENT_METRICS: 'department_metrics',
  MONTHLY_REPORT: 'monthly_report',
  YEARLY_REPORT: 'yearly_report',
} as const;

// TODO: Add more constants as needed during migration
// TODO: Consider using enums instead of const assertions for better type safety
// TODO: Add validation for constant values
// TODO: Consider moving some constants to environment variables
// TODO: Add proper localization support for labels
