import { features, actions } from "../constants/permission";

/**
 * Route definitions with their components and required permissions
 * This centralizes all route definitions and their permission requirements
 */
const routeDefinitions = {
  // Always accessible routes (no permission required)
  profile: {
    path: "/app/profile",
    permission: null // No permission required
  },
  dashboard: {
    path: "/app/dashboard",
    permission: null // No permission required - will redirect based on role
  },
  
  // Admin routes
  adminDashboard: {
    path: "/app/admin-dashboard",
    permission: { feat: 'All', act: actions.readAll }
  },
  
  // User routes
  userDashboard: {
    path: "/app/user-dashboard",
    permission: null // No permission required for user dashboard
  },
  
  // Department routes
  department: {
    path: "/app/department",
    permission: { feat: features.department, act: actions.read }
  },
  departmentForm: {
    path: "/app/department/form",
    permission: { feat: features.department, act: actions.create }
  },
  departmentEdit: {
    path: "/app/department/form/:id",
    permission: { feat: features.department, act: actions.update }
  },
  
  // Designation routes
  designation: {
    path: "/app/designation",
    permission: { feat: features.designation, act: actions.read }
  },
  designationForm: {
    path: "/app/designation/form",
    permission: { feat: features.designation, act: actions.create }
  },
  designationEdit: {
    path: "/app/designation/form/:id",
    permission: { feat: features.designation, act: actions.update }
  },
  
  // User management routes
  user: {
    path: "/app/user",
    permission: { feat: features.user, act: actions.read }
  },
  userForm: {
    path: "/app/user/form",
    permission: { feat: features.user, act: actions.create }
  },
  userEdit: {
    path: "/app/user/form/:id",
    permission: { feat: features.user, act: actions.update }
  },
  userCreate: {
    path: "/app/user/create",
    permission: { feat: features.user, act: actions.create }
  },
  
  // Attendance routes
  attendance: {
    path: "/app/attendance",
    permission: { feat: features.attendance, act: actions.read }
  },
  attendanceForm: {
    path: "/app/attendance/form",
    permission: { feat: features.attendance, act: actions.create }
  },
  attendanceEdit: {
    path: "/app/attendance/form/:id",
    permission: { feat: features.attendance, act: actions.update }
  },
  
  // Expenses routes
  expenses: {
    path: "/app/expenses",
    permission: { feat: features.expense, act: actions.read }
  },
  expensesForm: {
    path: "/app/expenses/form",
    permission: { feat: features.expense, act: actions.create }
  },
  expensesEdit: {
    path: "/app/expenses/form/:id",
    permission: { feat: features.expense, act: actions.update }
  },
  
  // Leave routes
  leave: {
    path: "/app/leave",
    permission: { feat: features.leave, act: actions.readAll }
  },
  leaveForm: {
    path: "/app/leave/form",
    permission: { feat: features.leave, act: actions.create }
  },
  leaveEdit: {
    path: "/app/leave/form/:id",
    permission: { feat: features.leave, act: actions.update }
  },
  leaveReport: {
    path: "/app/leave/report",
    permission: { feat: features.leavereport, act: actions.readAll }
  },
  leaveApproval: {
    path: "/app/leave/approval",
    permission: { feat: features.approve, act: actions.readAll }
  },
  leaveCalendar: {
    path: "/app/leave/calendar",
    permission: { feat: features.calendar, act: actions.readAll }
  },
  leaveConfiguration: {
    path: "/app/leave/configuration",
    permission: { feat: features.configuration, act: actions.readAll }
  },
  
  // User leave routes
  userLeave: {
    path: "/app/user/leave",
    permission: { feat: features.userleave, act: actions.readSelf }
  },
  
  // Setting routes
  setting: {
    path: "/app/setting",
    permission: { feat: features.setting, act: actions.update }
  },
  
  // Timeline routes
  timeline: {
    path: "/app/timeline",
    permission: { feat: features.timeline, act: actions.readAll }
  },
  timelineOverview: {
    path: "/app/timeline/overview",
    permission: { feat: features.overview, act: actions.readAll }
  },
  timelineRequest: {
    path: "/app/timeline/request",
    permission: { feat: features.timerequest, act: actions.readAll }
  },
  timelineTaskRequest: {
    path: "/app/timeline/taskrequest",
    permission: { feat: features.taskrequest, act: actions.readAll }
  },
  timelineWorkSchedule: {
    path: "/app/timeline/workschedule",
    permission: { feat: features.workschedule, act: actions.readAll }
  },
  
  // User timeline routes
  userTimeline: {
    path: "/app/user/timeline",
    permission: { feat: features.usertimeline, act: actions.read }
  },
  
  // Report routes
  report: {
    path: "/app/report",
    permission: { feat: features.report, act: actions.read }
  },
  
  // Project routes
  projectList: {
    path: "/app/project/list",
    permission: { feat: features.projectlist, act: actions.readAll }
  },
  projectOverview: {
    path: "/app/project/overview",
    permission: { feat: features.projectoverview, act: actions.readAll }
  },
  projectTimesheet: {
    path: "/app/project/timesheet",
    permission: { feat: features.projecttimesheet, act: actions.readAll }
  },
  
  // User projects routes
  userProjects: {
    path: "/app/user/projects",
    permission: { feat: features.projectsemployee, act: actions.read }
  },
  
  // Tasks routes
  tasks: {
    path: "/app/tasks",
    permission: { feat: "My Tasks", act: actions.read }
  },
  
  // Client routes
  client: {
    path: "/app/client",
    permission: { feat: features.client, act: actions.readAll }
  },
  
  // Sprint routes
  sprint: {
    path: "/app/sprint",
    permission: { feat: features.sprint, act: actions.readAll }
  },
  sprintForm: {
    path: "/app/sprint/form",
    permission: { feat: features.sprint, act: actions.create }
  },
  sprintEdit: {
    path: "/app/sprint/form/:id",
    permission: { feat: features.sprint, act: actions.update }
  },
  
  // User sprint routes
  userSprint: {
    path: "/app/user/sprint",
    permission: { feat: features.usersprint, act: actions.readSelf }
  }
};

/**
 * Generates route configuration objects for use in the routes.js file
 * @param {Object} components - Object mapping route keys to component references
 * @returns {Array} Array of route configuration objects
 */
export function generateRouteConfigs(components) {
  const routeConfigs = [];
  
  // Process each route definition
  Object.entries(routeDefinitions).forEach(([key, config]) => {
    const { path, permission } = config;
    
    // Get the component for this route
    const component = components[key];
    
    if (!component) {
      console.warn(`No component found for route key: ${key}`);
      return;
    }
    
    // Create the route config
    const routeConfig = {
      path,
      component
    };
    
    // Add permission if required
    if (permission) {
      routeConfig.permission = permission;
    }
    
    routeConfigs.push(routeConfig);
  });
  
  return routeConfigs;
}

export default routeDefinitions;