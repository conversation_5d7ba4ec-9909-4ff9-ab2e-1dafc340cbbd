# HRMS (Human Resource Management System) Module

This module handles all human resource management functionality including leave management, attendance tracking, and HR reporting.

## Features Covered
- Leave management and approval workflows
- Leave calendar and scheduling
- Leave configuration and policies
- Leave reporting and analytics
- Attendance tracking and monitoring
- HR dashboard and metrics

## Components
- Leave request forms and cards
- Leave calendar components
- Attendance tracking interfaces
- Leave approval workflows
- HR dashboard widgets

## Pages
- Leave dashboard
- Leave request and management
- Leave approval screens
- Leave calendar views
- Leave configuration
- Leave reporting
- Attendance management

## Services
- Leave management services
- Attendance tracking services
- Leave approval services
- HR reporting services

## Store
- Leave state management
- Attendance state management
- HR configuration state

## Migration Notes
Current screens to migrate:
- `/screens/Leave/*` → `/modules/hrms/pages/`
- `/screens/LeaveApproval/*` → `/modules/hrms/pages/`
- `/screens/LeaveCalendar/*` → `/modules/hrms/pages/`
- `/screens/LeaveConfiguration/*` → `/modules/hrms/pages/`
- `/screens/LeaveReport/*` → `/modules/hrms/pages/`
- `/screens/Attendance/*` → `/modules/hrms/pages/`

Current services to migrate:
- `LeaveService.js` → `modules/hrms/services/`
- `AttendanceService.js` → `modules/hrms/services/`

Current constants to migrate:
- `leaveConst.js` → `modules/hrms/constants/`
- `attendanceAction.js` → `modules/hrms/constants/`
