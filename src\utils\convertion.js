/**
 * Date and Data Conversion Utilities
 *
 * This module provides utility functions for data transformation
 * and date-based filtering operations.
 */

import moment from "moment";

/**
 * Groups an array of data objects by month based on their creation date
 *
 * @param {Array} data - Array of objects with createdAt property
 * @returns {Object} Object with month numbers as keys and arrays of items as values
 */
export function groupByMonth(data) {
    const months = {};

    if (Array.isArray(data)) {
        data.forEach(item => {
            // Extract month number (0-11) from the createdAt date
            const monthNumber = moment(item.createdAt).month();

            // Add item to the appropriate month array
            if (months[monthNumber]) {
                months[monthNumber].push(item);
            } else {
                months[monthNumber] = [item];
            }
        });
    }

    return months;
}

/**
 * Filters an array to return only items with a date matching today
 *
 * @param {Array} data - Array of objects with date property
 * @returns {Array} Filtered array containing only today's items
 */
export function getTodayData(data) {
    if (!Array.isArray(data)) {
        return [];
    }

    const today = moment();
    return data.filter(item => moment(item.date).isSame(today, 'day'));
}