/**
 * Application Entry Point
 *
 * This is the main entry file for the React application.
 * It sets up the React DOM, Redux store, and router.
 */

import React from "react";
import ReactDOM from "react-dom/client";
import { Provider } from "react-redux";
import reportWebVitals from "./reportWebVitals";

// Application components and styles
import App from "./App";
import store from "./store";
import "./index.css";

// Third-party styles
import "bootstrap/dist/css/bootstrap.min.css";

/**
 * Create the root React element and render the application
 */
const root = ReactDOM.createRoot(document.getElementById("root"));

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
);

// Report web vitals for performance monitoring
reportWebVitals();
