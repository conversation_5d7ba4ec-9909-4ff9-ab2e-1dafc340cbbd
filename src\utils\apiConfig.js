/**
 * API configuration utility
 * Centralizes API URL configuration to ensure consistency across the application
 */

// Base API URL - configured for Render deployment
// Use environment variable if available, otherwise fallback to defaults
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:10000/api';

// Log the API URL for debugging
console.log('API Base URL:', API_BASE_URL);

/**
 * Get the full API URL for a given endpoint
 * @param {string} endpoint - The API endpoint (without leading slash)
 * @returns {string} The full API URL
 */
export const getApiUrl = (endpoint) => {
  // Remove leading slash if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;

  // Get the base URL without the /api suffix if it exists
  let baseUrl = API_BASE_URL;
  if (!baseUrl.endsWith('/api')) {
    baseUrl += '/api';
  }

  // Ensure we don't have /api in the endpoint if the base already has it
  const cleanerEndpoint = cleanEndpoint.startsWith('api/') ? cleanEndpoint.substring(4) : cleanEndpoint;

  // Build the full URL and clean up any double slashes
  return `${baseUrl}/${cleanerEndpoint}`.replace(/(?:[^:]\/)\/+/g, "$1");
};

export default {
  API_BASE_URL,
  getApiUrl
};
