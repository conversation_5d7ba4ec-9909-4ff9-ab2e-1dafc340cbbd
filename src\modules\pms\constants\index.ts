/**
 * PMS Constants Index
 * 
 * This file exports all constants specific to the Project Management System module.
 * These constants include API endpoints, default values, and configuration options.
 * 
 * Usage:
 * import { PMS_ROUTES, PROJECT_STATUS_OPTIONS } from 'modules/pms/constants';
 */

// API Routes
export const PMS_ROUTES = {
  PROJECTS: '/api/projects',
  TASKS: '/api/tasks',
  SPRINTS: '/api/sprints',
  CLIENTS: '/api/clients',
  TIMELINE: '/api/timeline',
  TIMESHEET: '/api/timesheet',
} as const;

// Project Constants
export const PROJECT_STATUS_OPTIONS = [
  { value: 'planning', label: 'Planning', color: '#FFA500' },
  { value: 'in_progress', label: 'In Progress', color: '#007BFF' },
  { value: 'on_hold', label: 'On Hold', color: '#FFC107' },
  { value: 'completed', label: 'Completed', color: '#28A745' },
  { value: 'cancelled', label: 'Cancelled', color: '#DC3545' },
] as const;

export const PROJECT_PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', color: '#6C757D' },
  { value: 'medium', label: 'Medium', color: '#FFC107' },
  { value: 'high', label: 'High', color: '#FD7E14' },
  { value: 'critical', label: 'Critical', color: '#DC3545' },
] as const;

// Task Constants
export const TASK_STATUS_OPTIONS = [
  { value: 'todo', label: 'To Do', color: '#6C757D' },
  { value: 'in_progress', label: 'In Progress', color: '#007BFF' },
  { value: 'in_review', label: 'In Review', color: '#FFC107' },
  { value: 'done', label: 'Done', color: '#28A745' },
  { value: 'blocked', label: 'Blocked', color: '#DC3545' },
] as const;

export const TASK_PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', color: '#6C757D' },
  { value: 'medium', label: 'Medium', color: '#FFC107' },
  { value: 'high', label: 'High', color: '#FD7E14' },
  { value: 'urgent', label: 'Urgent', color: '#DC3545' },
] as const;

// Sprint Constants
export const SPRINT_STATUS_OPTIONS = [
  { value: 'planning', label: 'Planning', color: '#6C757D' },
  { value: 'active', label: 'Active', color: '#007BFF' },
  { value: 'completed', label: 'Completed', color: '#28A745' },
  { value: 'cancelled', label: 'Cancelled', color: '#DC3545' },
] as const;

export const SPRINT_DURATION_OPTIONS = [
  { value: 7, label: '1 Week' },
  { value: 14, label: '2 Weeks' },
  { value: 21, label: '3 Weeks' },
  { value: 28, label: '4 Weeks' },
] as const;

// Client Constants
export const CLIENT_STATUS_OPTIONS = [
  { value: 'active', label: 'Active', color: '#28A745' },
  { value: 'inactive', label: 'Inactive', color: '#6C757D' },
  { value: 'prospect', label: 'Prospect', color: '#FFC107' },
] as const;

// Timeline Constants
export const TIMELINE_EVENT_TYPES = [
  { value: 'created', label: 'Created', icon: 'add' },
  { value: 'updated', label: 'Updated', icon: 'edit' },
  { value: 'deleted', label: 'Deleted', icon: 'delete' },
  { value: 'status_changed', label: 'Status Changed', icon: 'swap_horiz' },
  { value: 'assigned', label: 'Assigned', icon: 'person_add' },
  { value: 'commented', label: 'Commented', icon: 'comment' },
] as const;

// Timesheet Constants
export const TIMESHEET_STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', color: '#6C757D' },
  { value: 'submitted', label: 'Submitted', color: '#007BFF' },
  { value: 'approved', label: 'Approved', color: '#28A745' },
  { value: 'rejected', label: 'Rejected', color: '#DC3545' },
] as const;

// Default Values
export const DEFAULT_PROJECT_VALUES = {
  status: 'planning',
  priority: 'medium',
  progress: 0,
  teamMembers: [],
  tags: [],
} as const;

export const DEFAULT_TASK_VALUES = {
  status: 'todo',
  priority: 'medium',
  estimatedHours: 0,
  actualHours: 0,
  tags: [],
  dependencies: [],
} as const;

export const DEFAULT_SPRINT_VALUES = {
  status: 'planning',
  capacity: 40, // hours
  taskIds: [],
} as const;

// Pagination Constants
export const PMS_PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const;

// Validation Constants
export const VALIDATION_RULES = {
  PROJECT_NAME_MIN_LENGTH: 3,
  PROJECT_NAME_MAX_LENGTH: 100,
  TASK_TITLE_MIN_LENGTH: 3,
  TASK_TITLE_MAX_LENGTH: 200,
  DESCRIPTION_MAX_LENGTH: 1000,
  MAX_TEAM_MEMBERS: 50,
  MAX_TAGS: 10,
  MIN_SPRINT_DURATION: 1,
  MAX_SPRINT_DURATION: 30,
  MAX_DAILY_HOURS: 24,
  MAX_WEEKLY_HOURS: 168,
} as const;

// Chart Colors
export const CHART_COLORS = {
  PRIMARY: '#007BFF',
  SUCCESS: '#28A745',
  WARNING: '#FFC107',
  DANGER: '#DC3545',
  INFO: '#17A2B8',
  SECONDARY: '#6C757D',
  LIGHT: '#F8F9FA',
  DARK: '#343A40',
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  INPUT: 'YYYY-MM-DD',
  DATETIME: 'MMM DD, YYYY HH:mm',
  TIME: 'HH:mm',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  PMS_FILTERS: 'pms_filters',
  PMS_PREFERENCES: 'pms_preferences',
  PMS_RECENT_PROJECTS: 'pms_recent_projects',
  PMS_RECENT_TASKS: 'pms_recent_tasks',
} as const;

// TODO: Add more constants as needed during migration
// TODO: Consider using enums instead of const assertions for better type safety
// TODO: Add validation for constant values
// TODO: Consider moving some constants to environment variables
