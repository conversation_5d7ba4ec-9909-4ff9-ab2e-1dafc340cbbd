import { createSlice } from "@reduxjs/toolkit";

export const CurrentTaskSlice = createSlice({
    name: "currenttask",
    initialState: {
        currentTask:{}
    },
    reducers: {
       setCurrentTaskData : (state,action) => {  
           state.currentTask = action.payload
           console.log("Setting Current task slice ",state.currentTask)
       },
       pauseCurrentTaskData : (state) => {
           state.currentTask = {...state.currentTask,taskStatus:"Pause"}
           console.log("Pause Current task slice ",state.currentTask)
       },
       deleteCurrentTaskData : (state) => {
            state.currentTask = {}
         console.log("Pause Current task slice ",state.currentTask)
       } 
    }
      
});

export default CurrentTaskSlice;