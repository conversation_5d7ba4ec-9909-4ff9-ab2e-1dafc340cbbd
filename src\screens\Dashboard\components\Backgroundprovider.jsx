import PropTypes from "prop-types";
import React, { createContext, useEffect, useState, useRef } from "react";
import { useDispatch,useSelector } from "react-redux";
import { ActivityActions,ProductActions,CurrentTaskActions,AttendanceActions } from "../../../slices/actions";
import { ActivitySelector } from "selectors/ActivitySelector";
import { CurrentTaskSelector, UserSelector } from "selectors";
import {setGlobalTimerState } from "../../../utils/timerUtils";

import socketService from "utils/socketService";


export const backContext = createContext();

function Backgroundprovider({ children }) {
  const dispatch = useDispatch();
  const [todayActivity, setActivity] = useState([]);
    const [socketController,setSocketController] = useState(false)
  const inactivityTimeout = useRef(null);
  const [isInactive, setIsInactive] = useState(false);
  const activities = useSelector(ActivitySelector.getActivityHistory())
  const todayActivityRef = useRef(null);
  const isInactiveRef = useRef(null);
  const [slotController,setSlotController] = useState(true)
  const [reloadTrigger, setReloadTrigger] = useState(0);
  const profile = useSelector(UserSelector.profile())
  const profileRef = useRef(null)
  const [currentTaskContext,setCurrentTaskContext] = useState({})
  const [timerSeconds, setTimerSeconds] = useState(0);
  const [runningTaskId, setRunningTaskId] = useState(null);
  const [runningTaskProjectId,setRunningTaskProjectId] = useState(null);

  const [showBreakPop, setShowBreakPop] = useState(false);
  const [lateCheckIn, setLateCheckIn] = useState(false);
  const [earlyCheckOut, setEarlyCheckOut] = useState(false);
  const [todayStatus, setTodayStatus] = useState(false);
  const [overLimitBreak, setOverLimitBreak] = useState(false);
  const [showGoalPopup, setShowGoalPopup] = useState(false);

  const [lunchType, setLunchType] = useState(
      localStorage.getItem("lunchType") || "Break In"
    );
      const currentTask = useSelector(CurrentTaskSelector.getCurrentTaskData())

  const [isRunning, setIsRunning] = useState(false);
  const[didByUser,setDidByUser] = useState("");
  const [op,setOp] = useState("");
useEffect(() => {

  if (profile?.role.includes("employee") || profile?.role.includes("admin")) {
    const productivityIn = setInterval(() => {
      
      dispatch(ActivityActions.getUserActivity({
        id: profile._id,
      }));
  
    }, 60000);

    return () => clearInterval(productivityIn);
  }
}, [profile, dispatch, todayActivityRef, isInactive]);

useEffect(() => {
  profileRef.current = profile;
  // console.log("Updated Profile Ref Current:", profileRef.current); // Debugging line
  
  if (!profile) {
    // Reset local state when profile is null (logged out)
    setActivity([]);
    setIsInactive(false);
    todayActivityRef.current = null;
    isInactiveRef.current = null;
    clearTimeout(inactivityTimeout.current);
    inactivityTimeout.current = null;
  }

}, [profile]);



useEffect(() => {
  const token = localStorage.getItem("merakihr-token");
  if (!profile || !token) { return; }

  // Ensure we connect once per token
  socketService.connect(token);

  socketService.on("task-updated", (taskData) => {
    console.log("Task updated via socket:", taskData);
    dispatch(ProductActions.updateTaskFromSocket(taskData)); 
  });

  socketService.on("timer-sync", (timerData) => {
    console.log("Timer sync data:", timerData);
    setGlobalTimerState(timerData);
  });


  socketService.on('check-in-update', (data) => {
       setReloadTrigger(prev => prev + 1);
  });

  socketService.on("break-in-update",(data) => {
      setReloadTrigger(prev => prev + 1);
      setLunchType(data.type)
      setSocketController(true)
      console.log("Break in update ",currentTask)
  })

  socketService.on("break-out-update",(data) => {
      setReloadTrigger(prev => prev + 1);
      setLunchType(data.type)
      setSocketController(true)
  })

  socketService.on("check-out-update",(data) => {
      setReloadTrigger(prev => prev + 1);
  })

  socketService.on('late-checkin-close',(data) => {
      setLateCheckIn(false)
  })

  socketService.on('early-checkout-close',(data) => {
    setEarlyCheckOut(false)
  })
  socketService.on('over-break-close',(data) => {
    setOverLimitBreak(false)
  })
  socketService.on('work-status-close',(data) => {
    setTodayStatus(false)
  })
  socketService.on('other-break-close',(data) => {
    setShowBreakPop(false)
  })
  socketService.on("today-goal-close",(data) => {
      setShowGoalPopup(false)
  })

  socketService.on('timer-update', (data) => {
  
      
    if (data && data.taskId) {
          // console.log("timer - update ",data)
          // Only update display for other users' timers
          // console.log("Task Data update ")
          setRunningTaskId(data.taskId);
          setRunningTaskProjectId(data.projectId)
          setTimerSeconds(data.seconds);
          setGlobalTimerState(data);
          setIsRunning(true)
          setOp(data?.op)
          setDidByUser(data?.didByUser)
        } else if (data?.op === "stop" && data?.didByUser === "yes") {
          // Reset when no timer is running
          //  console.log("timer - update ",data)
          setRunningTaskId(null);
          setRunningTaskProjectId(null);
          setTimerSeconds(0);
          setIsRunning(false);
          setGlobalTimerState(null);
        }
  });
  
      // Handle button state updates only for current user
  socketService.on('button-state-update', (data) => {
        
    // console.log("button-state-update",data)
        if (data.userId === profile._id) {
          setRunningTaskId(data.taskId || null);
          setRunningTaskProjectId(data.projectId || null);
          setIsRunning(data.isRunning || false);
          if (data.seconds !== undefined) {
            setTimerSeconds(data.seconds);
          }
        }
  });
  
      // Handle task events from desktop
  socketService.on('task-started', (data) => {
    if (data.userId === profile._id) {
      console.log("task-started",data)
          // Update state directly to avoid recursive calls
          setRunningTaskId(data.taskId);
            // console.log("task started ",data)
           
          setRunningTaskProjectId(data.projectId);
          setTimerSeconds(data.seconds || 0);
          setIsRunning(true);
          setGlobalTimerState({
            taskId: data.taskId,
            seconds: data.seconds || 0,
            isRunning: true,
            userId: profile._id
          });

          // if(data.didByUser === "yes") {

            localStorage.setItem("currentTask",JSON.stringify({taskId:data.taskId,projectId:data.projectId,taskStatus:"Start"}))
          // } 

          setOp(data?.op)
          setDidByUser(data?.didByUser)
   
          dispatch(ProductActions.getProductsByUser({ id: profile._id }));
          dispatch(ProductActions.getOnGoingProductsTasksByDate(new Date()))

        }
  });
  
  socketService.on('task-paused', (data) => {
        console.log("task-paused",data)
        if (data.userId === profile._id) {
          setIsRunning(false);
          setTimerSeconds(0);
          setGlobalTimerState({
            taskId: data.taskId,
            seconds: 0,
            isRunning: false,
            userId: profile._id
          });

          if(data.didByUser === "yes") {

            localStorage.removeItem("currentTask")
          }
           console.log(" task - paused ",JSON.parse(localStorage.getItem("currentTask")))

          dispatch(ProductActions.getProductsByUser({ id: profile._id }));
          dispatch(ProductActions.getOnGoingProductsTasksByDate(new Date()))
        }
  });
  
  socketService.on('task-stopped', (data) => {
        console.log("task-stopped ",data)
        if (data.userId === profile._id) {
          setRunningTaskId(null);
          setRunningTaskProjectId(null);
          setIsRunning(false);
          setTimerSeconds(0);
          setGlobalTimerState(null);
          dispatch(ProductActions.getProductsByUser({ id: profile._id }));
           if(data.didByUser === "yes") {
          localStorage.removeItem("currentTask")
           }
          dispatch(ProductActions.getOnGoingProductsTasksByDate(new Date()))
        }
  });

  socketService.on('get-updated-attendance', (data) => {
        console.log("get-update-attendance");
         const today = new Date();
         const formattedDate = today.toISOString().split("T")[0];
   
         // Force cache refresh by adding a timestamp
         const timestamp = Date.now();
   
         dispatch(
           AttendanceActions.getAttendances({
             user: profile._id,
             date: formattedDate,
             _t: timestamp, // Add timestamp to prevent caching
           })
         );
  })


    return () => {
      socketService.off('check-in-update');
      socketService.off('check-out-update');
      socketService.off("break-in-update");
      socketService.off("break-out-update");
      
      socketService.off("late-checkin-close");
      socketService.off("early-checkout-close");
      socketService.off("over-break-close");
      socketService.off("work-status-close");
      socketService.off("other-break-close");
      socketService.off("today-goal-close");
      socketService.off("get-updated-attendance");

      socketService.off("timer-update'");
      socketService.off("button-state-update");
      socketService.off("task-started");
      socketService.off("task-paused");
      socketService.off("task-stopped");

      socketService.disconnect();
    };

}, [profile, dispatch]);

useEffect(() => {
  console.log("Background is running ",isRunning,didByUser,op)
},[isRunning])

  useEffect(() => {
    let interval = null;
    if (isRunning && runningTaskId) {
      interval = setInterval(() => {
       if (isRunning && runningTaskId) {
         setTimerSeconds(prev => {
           const newSeconds = prev + 1;
           // Emit to desktop every 5 seconds to reduce network traffic
           if (socketService) {
            //  console.log("Timer -sync Op data ",isRunning,runningTaskId,op,didByUser)
             socketService.emit('timer-sync', {
               taskId: runningTaskId,
               projectId: runningTaskProjectId,
               seconds: newSeconds,
               isRunning: true,
               userId: profile._id,
               op:op,
               didByUser:didByUser
             });
           }
           return newSeconds;
         });

       }
      }, 1000);
    }
    return () => {
      if (interval) { clearInterval(interval) }
    };
  }, [isRunning, runningTaskId, profile]);

  useEffect(() => {

    if(profileRef.current?.role.includes("employee") || profileRef.current?.role.includes("admin")) {

      if (activities && activities.length > 0) {
        if (new Date(activities[activities.length - 1].checkInTime).getDate() === new Date().getDate()) {
          setActivity([activities[activities.length - 1]]);
          // console.warn("Should be Today actitivity : ", todayActivity);
        }
     
        setSlotController(true);
        todayActivityRef.current = todayActivity;
 
     
      }
    }

  }, [activities]);

  // use for pause the task
     const  handlePauseTask = async (taskId, projectId) => {
      try {
        console.log("Handle pause task called ",taskId,projectId,runningTaskId)
        if (runningTaskId !== taskId) { return }
  
        const today = new Date().toISOString().split("T")[0];
        dispatch(ProductActions.pauseTask({ 
          taskId, 
          projectId, 
          elapsedTime: timerSeconds,
          pauseTime: new Date().toISOString(),
          date: today,
          startTime: new Date().toISOString()
        }));
  
        // Pause timer - reset to 00:00:00
        setIsRunning(false);
        setTimerSeconds(0);
  
        const timerData = {
          taskId,
          projectId,
          seconds: 0,
          isRunning: false,
          userId: profile._id,
          didByUser: "no",
          op:"pause"
        };

        setOp(timerData.op);
      setDidByUser(timerData.didByUser);
  
        setGlobalTimerState(timerData);
  
        // Emit to desktop immediately
        if (socketService) {
          socketService.emit('timer-sync', timerData);
          socketService.emit('button-state-update', timerData);
        }
        // dispatch(CurrentTaskActions.pauseCurrentTaskData())
        dispatch(ProductActions.getProductsByUser({ id: profile._id }));
        setTimeout(() => {

          dispatch(ProductActions.getOnGoingProductsTasksByDate(new Date()));
        },1000)
  
      } catch (error) {
        console.error("❌ Web pause error:", error);
      }
    };
  
    // use for complete the task
    const handleStopTask = async (taskId, projectId) => {
      try {
        const today = new Date().toISOString().split("T")[0];
        dispatch(ProductActions.stopTask({ 
          taskId, 
          projectId, 
          elapsedTime: timerSeconds,
          date: today
        }));
  
        // Stop timer
        setRunningTaskId(null);
        setRunningTaskProjectId(null);
        setTimerSeconds(0);
        setIsRunning(false);
        setGlobalTimerState(null);
  
        // Emit to desktop immediately
        if (socketService) {
          socketService.emit('timer-sync', {op:"stop",didByUser:"no"});
          socketService.emit('button-state-update', {
            taskId: null,
            isRunning: false,
            seconds: 0,
            userId: profile._id,
            op:"stop",
            didByUser:"no"

          });
        }
        setOp("stop");
      setDidByUser("no");
        // dispatch(CurrentTaskActions.pauseCurrentTaskData())
        dispatch(ProductActions.getProductsByUser({ id: profile._id }));
          setTimeout(() => {

          dispatch(ProductActions.getOnGoingProductsTasksByDate(new Date()));
        },1000)
  
      } catch (error) {
        console.error("❌ Web stop error:", error);
      }
    };

    // use for the restart the task 
    const handleRestartTask = async (taskId, projectId) => {
       console.log("Handle restart task called ",taskId,projectId,runningTaskId,isRunning)
        try {
          if (runningTaskId && runningTaskId !== taskId && isRunning) {
            alert("Please stop the running task first.");
            return;
          }
    
          const today = new Date().toISOString().split("T")[0];
    
          dispatch(ProductActions.startTask({
            taskId,
            projectId,
            date: today
          }));
    
          setRunningTaskId(taskId);
          setRunningTaskProjectId(projectId)
          setTimerSeconds(0);
          setIsRunning(true);
    
          const timerData = {
            taskId,
            projectId,
            seconds: 0,
            isRunning: true,
            userId: profile._id,
            didByUser: "no",
            op:"start"
          };

         setOp(timerData.op);
      setDidByUser(timerData.didByUser);
        
          setGlobalTimerState(timerData);
    
          // Emit to desktop immediately
          if (socketService) {
            socketService.emit('timer-sync', timerData);
            socketService.emit('button-state-update', timerData);
          }
          dispatch(CurrentTaskActions.setCurrentTaskData({taskId,projectId,taskStatus:"Start"}))
          dispatch(ProductActions.getProductsByUser({ id: profile._id }));
          setTimeout(() => {

            dispatch(ProductActions.getOnGoingProductsTasksByDate(new Date()));
        },1000)
    
        } catch (error) {
          console.error("❌ Web restart error:", error);
        }
      }
    
  return <backContext.Provider value={{activities,runningTaskProjectId, todayActivity,profile,timerSeconds,runningTaskId,isRunning,reloadTrigger, lunchType,setTimerSeconds,currentTaskContext,setCurrentTaskContext,handlePauseTask,handleStopTask,setRunningTaskId,setIsRunning,setLunchType,setReloadTrigger,handleRestartTask,setSocketController,setRunningTaskProjectId,socketController,op,didByUser,setOp,setDidByUser,showBreakPop,setShowBreakPop,lateCheckIn,setLateCheckIn,earlyCheckOut,setEarlyCheckOut,todayStatus,setTodayStatus,overLimitBreak,setOverLimitBreak,showGoalPopup,setShowGoalPopup}}>{children}</backContext.Provider>;
}

Backgroundprovider.propTypes = {
  children: PropTypes.object,
};

export default Backgroundprovider;
