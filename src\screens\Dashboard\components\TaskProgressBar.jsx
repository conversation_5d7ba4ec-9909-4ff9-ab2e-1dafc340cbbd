import React, { useState, useEffect } from "react";
import { Tooltip } from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { ProductSelector, TimelineSelector, UserSelector } from "../../../selectors";
import { ProductActions, TimelineActions } from "../../../slices/actions";
import "../../../App.css";



const TaskProgressBar = () => {
  const dispatch = useDispatch();
  const timelineRequests = useSelector(
    TimelineSelector.getTimelineRequests()
  );
  const profile = useSelector(UserSelector.profile())
 const ongoingTasks = useSelector(ProductSelector.getOnGoingProductsTasksByDate());
  const [todayTaskArr, setTodaysTaskArr] = useState([]);
  const [toolTipTitle, setTooltipTitle] = useState("");
  const [toolTipController, setToolTipController] = useState(false);
  const [hoveredSlot, setHoveredSlot] = useState(null);

   useEffect(() => {
        console.log("Timeline requests ",timelineRequests)
    },[timelineRequests])

  useEffect(() => {
    const today = new Date();
    dispatch(
      TimelineActions.getTimelineRequestByDate({id:profile._id,date:today})
    );
    dispatch(ProductActions.getOnGoingProductsTasksByDate(new Date()));
   
  }, []);

  // Initial task loading effect
useEffect(() => {
  
  if (!ongoingTasks || ongoingTasks.length === 0) {
    setTodaysTaskArr([]); // clear when no tasks
    return;
  }

  const todayDate = new Date();
  todayDate.setHours(0, 0, 0, 0);

  const newTasks = ongoingTasks.flatMap((product) => {
    const currentTask = product.taskArr?.filter((task) =>
      ["Completed", "Pause", "In Progress", "To Do"].includes(task.taskStatus)
    ) || [];

    return currentTask.flatMap((task) => {
      return (task.pauseTimes || []).flatMap((data) => {
        const getTaskDate = new Date(data.startTime);
        getTaskDate.setHours(0, 0, 0, 0);

        if (getTaskDate.toString() !== todayDate.toString()) { return []; }

        const startTime = new Date(data.startTime);
        const pauseTime = data.pauseTime ? new Date(data.pauseTime) : null;
        const elapsed = pauseTime
          ? Math.floor((pauseTime - startTime) / 1000)
          : Math.floor((new Date() - startTime) / 1000);

        // let status = pauseTime && task.taskStatus !== "Completed"
        //   ? "Pause"
        //   : task.taskStatus;

        return [{
          uniqueId: data._id,
          startTime,
          taskTitle: task?.taskTitle || task.description || "Current Task",
          projectName: product?.productName || "Active Project",
          currentElapsed: elapsed,
          stopTime: pauseTime,
          taskStatus: task.taskStatus
        }];
      });
    });
  });

  // Replace the old array completely with new sorted unique tasks
  const uniqueTasks = newTasks.filter(
    (task, index, self) =>
      index === self.findIndex(t => t.uniqueId === task.uniqueId)
  );

  setTodaysTaskArr(uniqueTasks.sort((a, b) => a.startTime - b.startTime));

  console.log("Updated Today's Task Array:", uniqueTasks);
}, [ongoingTasks]);


  // Effect for updating elapsed time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setTodaysTaskArr((prevTasks) => {
        const now = new Date();
        const updatedTasks = prevTasks.map((task) => {
           
          if (task.taskStatus === "In Progress" && !task.stopTime) {
            const newElapsed = Math.floor((now - new Date(task.startTime)) / 1000);
            return {
              ...task,
              currentElapsed: newElapsed,
             
            };
          }
          return task;
        });
        return updatedTasks;
      });
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []); // Empty dependency array means this runs once on mount

  const hours = Array.from({ length: 24 }).map((_, i) => {
    if (i === 0) {
      return "12AM";
    }
    if (i < 12) {
      return `${i}AM`;
    }
    if (i === 12) {
      return "12PM";
    }
    return `${i - 12}PM`;
  });

  const minArr = Array.from({ length: 60 }, (_, i) => i);

  function formatTo12Hour(time24) {
  // Split the input into hours, minutes, and seconds
  let [hours, minutes, seconds] = time24.split(':').map(Number);

  // Determine AM or PM
  const ampm = hours >= 12 ? 'PM' : 'AM';

  // Convert hours to 12-hour format
  hours = hours % 12 || 12;

  // Return formatted string
  return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')} ${ampm}`;
}

  const getSlotColor = (hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);



      if(timelineRequests?.length > 0) {
           
        for(let i=0; i<timelineRequests.length; i++) {
            if(timelineRequests[i]?.taskDetails.taskId){
                 const start = new Date(timelineRequests[i].fromTime);
                 const stop = new Date(timelineRequests[i].toTime);
                if (slotTime >= start && slotTime <= stop) {
                    if(timelineRequests[i].taskDetails.status === "pending" && !timelineRequests[i].taskDetails.updatedByAdmin){
                     
                        return "#ff8000ff"
                    } 
                }

            }
        }
      }

    if (todayTaskArr?.length > 0) {
      let finalColor = "#E0E0E0"; // Default color


      for (let i = 0; i < todayTaskArr.length; i++) {
        const task = todayTaskArr[i];
        const start = new Date(task.startTime);
        const stop = !task.stopTime 
          ? new Date() // Use current time for in-progress tasks
          : new Date(task.stopTime);

        if (slotTime >= start && slotTime < stop) {
          if (task.taskStatus === "In Progress") {
            return "#00c3ffff";
          } else if (task.taskStatus === "Pause" || task.taskStatus === "To Do") {
            console.log("second condition")
            finalColor = "#1e3246";
          } else if (task.taskStatus === "Completed") {
            finalColor = "#a91f7fff";
          }
        } 
       
      }

      return finalColor;
    }

    return "#E0E0E0";
  };

  const handleMouseEnter = (hour, minute) => {
    const slotTime = new Date();
    slotTime.setHours(hour, minute, 0, 0);
    const slotKey = `${hour}-${minute}`;
    setHoveredSlot(slotKey);

    for(let i = 0; i < timelineRequests.length; i++) {
          const task = timelineRequests[i].description;
      const taskStartTime = new Date(timelineRequests[i].fromTime);
      // const taskStopTime = task.stopTime ? new Date(task.stopTime) : new Date()
      const stop = new Date(timelineRequests[i].toTime);
      
      if (slotTime >= taskStartTime && slotTime < stop) {
        const status = timelineRequests[i].taskDetails.status;
        const tooltipContent = `Task title: ${
          timelineRequests[i].taskDetails.taskTitle
        }\nStatus: ${status}
        ${formatTo12Hour(taskStartTime.toLocaleTimeString())} - ${formatTo12Hour(stop.toLocaleTimeString())}`;
        setTooltipTitle(tooltipContent);
        setToolTipController(true);
        return;
      }
    }

    for (let i = 0; i < todayTaskArr.length; i++) {
      const task = todayTaskArr[i];
      const taskStartTime = new Date(task.startTime);
      // const taskStopTime = task.stopTime ? new Date(task.stopTime) : new Date()
      const stop = !task.stopTime 
        ? new Date() 
        : new Date(task.stopTime);
      
      if (slotTime >= taskStartTime && slotTime < stop) {
        const status = task.taskStatus;
        const tooltipContent = `🔄 LIVE TASK\nTask: ${
          task.taskTitle
        }\nProject: ${
          task.projectName
        }\nStatus: ${status}
        ${formatTo12Hour(taskStartTime.toLocaleTimeString())} - ${formatTo12Hour(stop.toLocaleTimeString())}`;
        setTooltipTitle(tooltipContent);
        setToolTipController(true);
        return;
      }
    }

    setTooltipTitle("");
    setToolTipController(false);
  };

  const handleMouseLeave = () => {
    setTooltipTitle("");
    setToolTipController(false);
    setHoveredSlot(null);
  };

  const renderProgressBars = () => {
    const progressBars = [];

    hours.forEach((_, hourIndex) => {
      minArr.forEach((minute) => {
        const activityColor = getSlotColor(hourIndex, minute);
        const slotKey = `${hourIndex}-${minute}`;

        progressBars.push(
          <div
            key={slotKey}
            className="progress-bar-slot"
            style={{
              width: "1.04%",
              height: "100%",
              backgroundColor: activityColor,
              display: "inline-block",
              position: "relative",
            }}
            onMouseEnter={() => handleMouseEnter(hourIndex, minute)}
            onMouseLeave={handleMouseLeave}
          >
            {toolTipController && toolTipTitle && hoveredSlot === slotKey && (
              <Tooltip
                title={
                  <div style={{ whiteSpace: "pre-line" }}>{toolTipTitle}</div>
                }
                arrow
                open={true}
                placement="top"
              >
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    pointerEvents: "none",
                  }}
                />
              </Tooltip>
            )}
          </div>
        );
      });
    });

    return progressBars;
  };

  return (
    <>
      <div style={{ marginBottom: "1px" }}>
        <div className="progress" style={{ height: "10px" }}>
          {renderProgressBars()}
        </div>
      </div>

      {/* Time Labels */}
      <div className="d-flex justify-content-between">
        {hours.map((label, index) => (
          <li key={index} className="timeSlotLi">
            {label}
          </li>
        ))}
      </div>
    </>
  );
};



export default TaskProgressBar;