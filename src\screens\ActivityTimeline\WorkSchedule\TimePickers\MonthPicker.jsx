import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  IconButton,
  Typography,
  Box,
  Popper,
  Paper,
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import dayjs from "dayjs";
import PropTypes from "prop-types";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";

/**
 * MonthPicker Component
 * Allows users to pick a month using a date calendar
 */
const MonthPicker = ({ onChange, selectedMonth }) => {
  // State to track the selected month
  const [selectedDate, setSelectedDate] = useState(dayjs(selectedMonth || new Date()));

  // State to manage the visibility of the date picker
  const [openPicker, setOpenPicker] = useState(false);

  // Refs for detecting outside clicks
  const calendarRef = useRef(null);
  const calendarContainerRef = useRef(null);

  // Ref to track the last sent date range to prevent duplicate calls
  const lastSentRange = useRef(null);

  // Ref to track if we're in the middle of an external update
  const isExternalUpdate = useRef(false);

  // Sync with external selectedMonth prop changes
  useEffect(() => {
    if (selectedMonth && !isExternalUpdate.current) {
      const newDate = dayjs(selectedMonth);

      // Only update if it's a different month to prevent loops
      if (!newDate.isSame(selectedDate, 'month')) {
        isExternalUpdate.current = true;
        setSelectedDate(newDate);
        // Reset the flag after a short delay to allow state to settle
        setTimeout(() => {
          isExternalUpdate.current = false;
        }, 100);
      }
    }
  }, [selectedMonth]); // Remove selectedDate from dependencies to prevent loops

  // Call onChange callback whenever selectedDate changes - fixed callback signature
  useEffect(() => {
    // Skip if this is an external update to prevent circular calls
    if (isExternalUpdate.current) {
      return;
    }

    const startOfMonth = selectedDate.startOf('month');
    const endOfMonth = selectedDate.endOf('month');

    const formattedStart = startOfMonth.format("YYYY-MM-DD");
    const formattedEnd = endOfMonth.format("YYYY-MM-DD");

    // Check if this is the same as the last sent range
    const currentRange = `${formattedStart}-${formattedEnd}`;
    if (lastSentRange.current !== currentRange && onChange) {
      lastSentRange.current = currentRange;

      // Pass the date range in the expected format (direct object, not function)
      onChange({
        startDate: formattedStart,
        endDate: formattedEnd
      });
    }
  }, [selectedDate, onChange]);
  
  // Handle month selection when a new date is picked
  const handleDateChange = (date) => {
    if (date) {
      setSelectedDate(dayjs(date));
      setOpenPicker(false);
    }
  };

  // Close the date picker when clicking outside
  const handleClickOutside = useCallback((event) => {
    if (
      calendarContainerRef.current &&
      !calendarContainerRef.current.contains(event.target) &&
      calendarRef.current &&
      !calendarRef.current.contains(event.target)
    ) {
      setOpenPicker(false);
    }
  }, []);
  
  useEffect(() => {
    if (openPicker) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openPicker, handleClickOutside]);
  
  // Handlers to navigate months
  const goToPreviousMonth = () => setSelectedDate((prev) => prev.subtract(1, "month"));
  const goToNextMonth = () => setSelectedDate((prev) => prev.add(1, "month"));

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box display="flex" justifyContent="flex-end" alignItems="center" gap={1} position="relative">
        {/* Previous Month Button */}
        <IconButton onClick={goToPreviousMonth}>
          <ChevronLeftIcon sx={{ color: "grey.500" }} />
        </IconButton>

        {/* Display Selected Month */}
        <Typography
          variant="body1"
          sx={{
            color: "text.secondary",
            borderRadius: "4px",
            padding: "4px 8px",
          }}
        >
          {selectedDate.format("MMMM YYYY")}
        </Typography>

        {/* Calendar Icon Button */}
        <IconButton ref={calendarRef} onClick={() => setOpenPicker((prev) => !prev)}>
          <CalendarTodayIcon sx={{ color: "grey.500" }} />
        </IconButton>

        {/* Popper to display the Calendar */}
        <Popper
          ref={calendarContainerRef}
          open={openPicker}
          anchorEl={calendarRef.current}
          placement="bottom"
          disablePortal
          modifiers={[
            {
              name: "preventOverflow",
              options: { boundary: "window" },
            },
          ]}
        >
          <Paper elevation={3} sx={{ p: 1, position: "relative", right: "100px" }}>
            <DateCalendar
              value={selectedDate}
              onChange={handleDateChange}
              views={['month', 'year']}
              openTo="month"
            />
          </Paper>
        </Popper>

        {/* Next Month Button */}
        <IconButton onClick={goToNextMonth}>
          <ChevronRightIcon sx={{ color: "grey.500" }} />
        </IconButton>
      </Box>
    </LocalizationProvider>
  );
};

MonthPicker.propTypes = {
  onChange: PropTypes.func,
  selectedMonth: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
};

export default MonthPicker;
