{"name": "meraki-frontend", "version": "0.1.0", "private": true, "dependencies": {"@babel/runtime": "^7.26.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.16.14", "@mui/lab": "^6.0.0-beta.28", "@mui/material": "^6.4.5", "@mui/system": "^7.2.0", "@mui/x-date-pickers-pro": "^7.23.3", "@reduxjs/toolkit": "^1.9.7", "@testing-library/react": "^11.1.0", "apexcharts": "^5.3.1", "axios": "^1.7.7", "bootstrap": "^5.3.3", "chart.js": "^4.4.7", "connected-react-router": "^6.9.1", "date-fns": "^2.29.3", "dayjs": "^1.11.13", "eslint-plugin-react": "^7.26.1", "eslint-visitor-keys": "^2.1.0", "formik": "^2.2.9", "history": "4.10.1", "lodash": "^4.17.21", "moment": "^2.29.1", "postcss": "^8.4.49", "react": "^18.2.0", "react-apexcharts": "^1.3.9", "react-bootstrap": "^2.10.6", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^5.3.4", "react-scripts": "^5.0.1", "react-toastify": "^10.0.6", "recharts": "^2.14.1", "redux-saga": "^1.3.0", "socket.io-client": "^4.8.1", "styled-components": "^6.1.13", "web-vitals": "^5.0.2", "yup": "^0.32.9"}, "scripts": {"start": "craco start", "build": "react-scripts build", "test": "craco test", "eject": "craco eject", "lint": "eslint src/**/*.js --fix", "heroku-postbuild": "npm install && npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@craco/craco": "^5.9.0", "eslint": "^8.57.1", "prop-types": "^15.7.2"}}