# Common Module

This module contains shared utilities, components, hooks, services, and constants that are used across all other modules (PMS, HRMS, EMS).

## Features Covered
- Reusable UI components
- Shared layout components
- Common React hooks
- Shared API services and configuration
- Global constants and enums
- Utility functions
- Global state management
- Shared TypeScript types

## Components
- Reusable UI components (buttons, forms, tables, etc.)
- Layout wrappers (DashboardLayout, AuthLayout, etc.)
- Common widgets and cards
- Shared modals and dialogs

## Layouts
- DashboardLayout - Main application layout
- AuthLayout - Authentication pages layout
- ErrorLayout - Error pages layout
- PrintLayout - Print-friendly layout

## Hooks
- Custom React hooks for common functionality
- API hooks for data fetching
- Form hooks for validation
- UI state hooks

## Services
- Shared API configuration
- Authentication services
- File upload services
- Notification services
- Socket services

## Constants
- Global application constants
- API endpoints
- Configuration values
- Enum definitions

## Utils
- General helper functions
- Data transformation utilities
- Validation utilities
- Formatting utilities

## Store
- Global state management
- Authentication state
- UI state (theme, notifications, etc.)
- Shared selectors and actions

## Migration Notes
Current files to migrate:
- `/components/*` → `/modules/common/components/`
- `/layouts/*` → `/modules/common/layouts/`
- `/hooks/*` → `/modules/common/hooks/`
- `/services/*` (shared ones) → `/modules/common/services/`
- `/constants/*` (global ones) → `/modules/common/constants/`
- `/utils/*` → `/modules/common/utils/`

## Design Principles
- Keep components generic and reusable
- Avoid module-specific business logic
- Use proper TypeScript interfaces
- Follow consistent naming conventions
- Implement proper error boundaries
- Add comprehensive documentation
