import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, Button, Paper } from '@mui/material';
import { useHistory } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import LockIcon from '@mui/icons-material/Lock';

/**
 * Component to display when a user tries to access a route they don't have permission for
 */
const AccessDenied = ({ feature, action }) => {
  const history = useHistory();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '70vh',
        p: 3
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          maxWidth: 600,
          textAlign: 'center',
          borderTop: '4px solid #f44336'
        }}
      >
        <LockIcon sx={{ fontSize: 60, color: '#f44336', mb: 2 }} />

        <Typography variant="h4" gutterBottom>
          Access Denied
        </Typography>

        <Typography variant="body1" paragraph>
          You don&apos;t have permission to access this page.
        </Typography>

        {feature && action && (
          <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="body2" color="textSecondary">
              Required Permission:
            </Typography>
            <Typography variant="body1">
              <strong>{feature}</strong> ({action})
            </Typography>
          </Box>
        )}

        <Typography variant="body2" paragraph color="textSecondary">
          Please contact your administrator if you believe you should have access to this page.
        </Typography>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              // Check if we're already on a dashboard route to prevent loops
              const currentPath = history.location.pathname;
              if (currentPath.includes('dashboard')) {
                // If already on a dashboard route, go to root
                history.push('/');
              } else {
                // Otherwise go to user dashboard
                history.push('/app/user-dashboard');
              }
            }}
          >
            Go to Dashboard
          </Button>
          <Button
            variant="outlined"
            onClick={() => history.goBack()}
          >
            Go Back
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

// PropTypes validation
AccessDenied.propTypes = {
  feature: PropTypes.string,
  action: PropTypes.string
};

// Default props
AccessDenied.defaultProps = {
  feature: null,
  action: null
};

export default AccessDenied;
