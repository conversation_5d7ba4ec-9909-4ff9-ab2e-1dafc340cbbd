/**
 * EMS Constants Index
 * 
 * This file exports all constants specific to the Employee Management System module.
 * These constants include API endpoints, default values, and configuration options.
 * 
 * Usage:
 * import { EMS_ROUTES, EMPLOYEE_STATUS_OPTIONS } from 'modules/ems/constants';
 */

// API Routes
export const EMS_ROUTES = {
  EMPLOYEES: '/api/employees',
  USERS: '/api/users',
  DEPARTMENTS: '/api/departments',
  DESIGNATIONS: '/api/designations',
  PROFILES: '/api/profiles',
  ROLES: '/api/roles',
  PERMISSIONS: '/api/permissions',
  ONBOARDING: '/api/onboarding',
  ORGANIZATION: '/api/organization',
  EMPLOYEE_REPORTS: '/api/employee-reports',
} as const;

// Employee Status Constants
export const EMPLOYMENT_STATUS_OPTIONS = [
  { value: 'active', label: 'Active', color: '#28A745' },
  { value: 'inactive', label: 'Inactive', color: '#6C757D' },
  { value: 'probation', label: 'Probation', color: '#FFC107' },
  { value: 'notice_period', label: 'Notice Period', color: '#FD7E14' },
  { value: 'terminated', label: 'Terminated', color: '#DC3545' },
  { value: 'resigned', label: 'Resigned', color: '#17A2B8' },
] as const;

// Employee Type Constants
export const EMPLOYEE_TYPE_OPTIONS = [
  { value: 'permanent', label: 'Permanent', color: '#28A745' },
  { value: 'contract', label: 'Contract', color: '#007BFF' },
  { value: 'intern', label: 'Intern', color: '#FFC107' },
  { value: 'consultant', label: 'Consultant', color: '#6F42C1' },
  { value: 'part_time', label: 'Part Time', color: '#17A2B8' },
] as const;

// Gender Constants
export const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
] as const;

// Marital Status Constants
export const MARITAL_STATUS_OPTIONS = [
  { value: 'single', label: 'Single' },
  { value: 'married', label: 'Married' },
  { value: 'divorced', label: 'Divorced' },
  { value: 'widowed', label: 'Widowed' },
] as const;

// Work Location Constants
export const WORK_LOCATION_OPTIONS = [
  { value: 'office', label: 'Office', icon: 'business' },
  { value: 'remote', label: 'Remote', icon: 'home' },
  { value: 'hybrid', label: 'Hybrid', icon: 'swap_horiz' },
  { value: 'client_site', label: 'Client Site', icon: 'location_on' },
] as const;

// Address Type Constants
export const ADDRESS_TYPE_OPTIONS = [
  { value: 'permanent', label: 'Permanent Address' },
  { value: 'current', label: 'Current Address' },
  { value: 'temporary', label: 'Temporary Address' },
] as const;

// Document Type Constants
export const DOCUMENT_TYPE_OPTIONS = [
  { value: 'resume', label: 'Resume', icon: 'description' },
  { value: 'id_proof', label: 'ID Proof', icon: 'badge' },
  { value: 'address_proof', label: 'Address Proof', icon: 'home' },
  { value: 'education_certificate', label: 'Education Certificate', icon: 'school' },
  { value: 'experience_letter', label: 'Experience Letter', icon: 'work' },
  { value: 'offer_letter', label: 'Offer Letter', icon: 'mail' },
  { value: 'contract', label: 'Contract', icon: 'gavel' },
  { value: 'other', label: 'Other', icon: 'insert_drive_file' },
] as const;

// Onboarding Task Type Constants
export const ONBOARDING_TASK_TYPE_OPTIONS = [
  { value: 'documentation', label: 'Documentation', icon: 'description', color: '#007BFF' },
  { value: 'system_access', label: 'System Access', icon: 'vpn_key', color: '#28A745' },
  { value: 'training', label: 'Training', icon: 'school', color: '#FFC107' },
  { value: 'orientation', label: 'Orientation', icon: 'explore', color: '#17A2B8' },
  { value: 'equipment', label: 'Equipment', icon: 'computer', color: '#6F42C1' },
  { value: 'introduction', label: 'Introduction', icon: 'people', color: '#FD7E14' },
  { value: 'policy_review', label: 'Policy Review', icon: 'policy', color: '#DC3545' },
] as const;

// Onboarding Task Status Constants
export const ONBOARDING_TASK_STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending', color: '#6C757D' },
  { value: 'in_progress', label: 'In Progress', color: '#007BFF' },
  { value: 'completed', label: 'Completed', color: '#28A745' },
  { value: 'skipped', label: 'Skipped', color: '#FFC107' },
  { value: 'overdue', label: 'Overdue', color: '#DC3545' },
] as const;

// Onboarding Status Constants
export const ONBOARDING_STATUS_OPTIONS = [
  { value: 'not_started', label: 'Not Started', color: '#6C757D' },
  { value: 'in_progress', label: 'In Progress', color: '#007BFF' },
  { value: 'completed', label: 'Completed', color: '#28A745' },
  { value: 'on_hold', label: 'On Hold', color: '#FFC107' },
  { value: 'cancelled', label: 'Cancelled', color: '#DC3545' },
] as const;

// Default Values
export const DEFAULT_EMPLOYEE_VALUES = {
  employmentStatus: 'active',
  employeeType: 'permanent',
  workLocation: 'office',
  isActive: true,
  skills: [],
  certifications: [],
  documents: [],
  emergencyContacts: [],
} as const;

export const DEFAULT_DEPARTMENT_VALUES = {
  isActive: true,
  employeeCount: 0,
  subDepartments: [],
} as const;

export const DEFAULT_DESIGNATION_VALUES = {
  level: 1,
  isActive: true,
  employeeCount: 0,
  responsibilities: [],
  requirements: [],
} as const;

export const DEFAULT_PROFILE_VALUES = {
  completeness: 0,
  socialLinks: [],
  preferences: {
    theme: 'light',
    language: 'en',
    timezone: 'UTC',
    notifications: {
      email: true,
      push: true,
      sms: false,
      inApp: true,
    },
  },
} as const;

// Validation Constants
export const VALIDATION_RULES = {
  EMPLOYEE_ID_MIN_LENGTH: 3,
  EMPLOYEE_ID_MAX_LENGTH: 20,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_MAX_LENGTH: 100,
  PHONE_MIN_LENGTH: 10,
  PHONE_MAX_LENGTH: 15,
  DEPARTMENT_NAME_MIN_LENGTH: 2,
  DEPARTMENT_NAME_MAX_LENGTH: 100,
  DEPARTMENT_CODE_MIN_LENGTH: 2,
  DEPARTMENT_CODE_MAX_LENGTH: 10,
  DESIGNATION_TITLE_MIN_LENGTH: 2,
  DESIGNATION_TITLE_MAX_LENGTH: 100,
  DESIGNATION_CODE_MIN_LENGTH: 2,
  DESIGNATION_CODE_MAX_LENGTH: 10,
  BIO_MAX_LENGTH: 500,
  ADDRESS_MAX_LENGTH: 200,
  MAX_EMERGENCY_CONTACTS: 3,
  MAX_CERTIFICATIONS: 20,
  MAX_DOCUMENTS: 50,
  MAX_SKILLS: 50,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif'],
  ALLOWED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'txt'],
} as const;

// Profile Completeness Weights
export const PROFILE_COMPLETENESS_WEIGHTS = {
  BASIC_INFO: 30, // Name, email, phone
  EMPLOYMENT_INFO: 25, // Department, designation, joining date
  PERSONAL_INFO: 20, // DOB, gender, address
  CONTACT_INFO: 15, // Emergency contacts
  ADDITIONAL_INFO: 10, // Skills, certifications, bio
} as const;

// Organization Chart Configuration
export const ORG_CHART_CONFIG = {
  MAX_LEVELS: 10,
  MAX_CHILDREN_PER_NODE: 20,
  NODE_WIDTH: 200,
  NODE_HEIGHT: 100,
  LEVEL_HEIGHT: 150,
  HORIZONTAL_SPACING: 50,
} as const;

// Pagination Constants
export const EMS_PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const;

// Chart Colors for Employee Analytics
export const EMS_CHART_COLORS = {
  ACTIVE: '#28A745',
  INACTIVE: '#6C757D',
  PROBATION: '#FFC107',
  PERMANENT: '#007BFF',
  CONTRACT: '#17A2B8',
  INTERN: '#FD7E14',
  MALE: '#007BFF',
  FEMALE: '#E83E8C',
  OTHER: '#6F42C1',
  PRIMARY: '#007BFF',
  SECONDARY: '#6C757D',
  SUCCESS: '#28A745',
  WARNING: '#FFC107',
  DANGER: '#DC3545',
  INFO: '#17A2B8',
} as const;

// Date Formats
export const EMS_DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  INPUT: 'YYYY-MM-DD',
  DATETIME: 'MMM DD, YYYY HH:mm',
  TIME: 'HH:mm',
  MONTH_YEAR: 'MMM YYYY',
  DAY_MONTH: 'DD MMM',
  FULL: 'dddd, MMMM DD, YYYY',
} as const;

// Local Storage Keys
export const EMS_STORAGE_KEYS = {
  EMPLOYEE_FILTERS: 'ems_employee_filters',
  DEPARTMENT_FILTERS: 'ems_department_filters',
  DESIGNATION_FILTERS: 'ems_designation_filters',
  EMS_PREFERENCES: 'ems_preferences',
  RECENT_EMPLOYEES: 'ems_recent_employees',
  DASHBOARD_SETTINGS: 'ems_dashboard_settings',
  ORG_CHART_SETTINGS: 'ems_org_chart_settings',
} as const;

// Social Media Platforms
export const SOCIAL_PLATFORMS = [
  { value: 'linkedin', label: 'LinkedIn', icon: 'linkedin', color: '#0077B5' },
  { value: 'twitter', label: 'Twitter', icon: 'twitter', color: '#1DA1F2' },
  { value: 'github', label: 'GitHub', icon: 'github', color: '#333' },
  { value: 'facebook', label: 'Facebook', icon: 'facebook', color: '#1877F2' },
  { value: 'instagram', label: 'Instagram', icon: 'instagram', color: '#E4405F' },
] as const;

// Countries List (Top 20 most common)
export const COUNTRIES = [
  'United States', 'Canada', 'United Kingdom', 'Australia', 'Germany',
  'France', 'India', 'China', 'Japan', 'Brazil', 'Mexico', 'Italy',
  'Spain', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Switzerland',
  'Singapore', 'New Zealand'
] as const;

// Languages
export const LANGUAGES = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'zh', label: 'Chinese' },
  { value: 'ja', label: 'Japanese' },
  { value: 'ko', label: 'Korean' },
  { value: 'hi', label: 'Hindi' },
] as const;

// Timezones (Common ones)
export const TIMEZONES = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
] as const;

// TODO: Add more constants as needed during migration
// TODO: Consider using enums instead of const assertions for better type safety
// TODO: Add validation for constant values
// TODO: Consider moving some constants to environment variables
// TODO: Add proper localization support for labels
