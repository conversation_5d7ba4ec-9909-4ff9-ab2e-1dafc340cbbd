import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Grid,
  Card,
  CardMedia,
  CircularProgress,
  TextField,
  Dialog,
  DialogContent,
  IconButton
} from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { format } from 'date-fns'
import { screenshotApi } from '../../utils/screenshotApi'
import { useParams } from "react-router-dom";
import { useSelector } from 'react-redux';
import { UserSelector } from 'selectors'
import { ArrowBackIos, ArrowForwardIos } from '@mui/icons-material'

function ScreenShotsDetails() {
  const [screenshots, setScreenshots] = useState([])
  const [loading, setLoading] = useState(false)
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [error, setError] = useState('')
  const [openImageIndex, setOpenImageIndex] = useState(null)

  const users = useSelector(UserSelector.getUsers()) || []
  const { id } = useParams()
  const userId = id

  const fetchScreenshots = async (date) => {
    setLoading(true)
    setError('')
    try {
      const dateString = format(date, 'yyyy-MM-dd')
      const data = await screenshotApi.getScreenshotsByUserId(userId, dateString)
      setScreenshots(data)
    } catch (error) {
      console.error('Error fetching screenshots:', error)
      setError('Failed to fetch screenshots. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (userId) {
      fetchScreenshots(selectedDate)
    }
  }, [selectedDate, userId])

  const handleDateChange = (newDate) => {
    setSelectedDate(newDate)
  }

  const handleImageClick = (index) => {
    setOpenImageIndex(index)
  }

  const handleCloseDialog = () => {
    setOpenImageIndex(null)
  }

  const handlePrev = () => {
    setOpenImageIndex((prev) => (prev > 0 ? prev - 1 : screenshots.length - 1))
  }

  const handleNext = () => {
    setOpenImageIndex((prev) => (prev < screenshots.length - 1 ? prev + 1 : 0))
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>Screenshots</Typography>

      <Box sx={{ mb: 3 }}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Select Date"
            value={selectedDate}
            onChange={handleDateChange}
            maxDate={new Date()}
            renderInput={(params) => <TextField {...params} fullWidth />}
          />
        </LocalizationProvider>
      </Box>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Typography color="error" variant="body1" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      {!loading && screenshots.length === 0 && (
        <Typography variant="body1" color="textSecondary">
          No screenshots found for {format(selectedDate, 'yyyy-MM-dd')}
        </Typography>
      )}

      <Grid container spacing={2}>
        {screenshots.map((screenshot, index) => {
          const time = new Date(screenshot.captureDate).toLocaleTimeString()
          const date = new Date(screenshot.captureDate).toLocaleDateString()
          return (
            <Grid item xs={12} sm={6} md={4} key={screenshot._id}>
              <Card sx={{ cursor: 'pointer' }} onClick={() => handleImageClick(index)}>
                <CardMedia
                  component="img"
                  height="200"
                  width="200"
                  image={`data:image/png;base64,${screenshot.imageData}`}
                  alt={`Screenshot at ${time}`}
                />
                <Box sx={{ p: 2 }}>
                  <Typography variant="body2" color="textSecondary">Time: {time}</Typography>
                  <Typography variant="body2" color="textSecondary">Key Counts: {screenshot.keyCounts}</Typography>
                  <Typography variant="body2" color="textSecondary">Date: {date}</Typography>
                </Box>
              </Card>
            </Grid>
          )
        })}
      </Grid>
<Dialog
  open={openImageIndex !== null}
  onClose={handleCloseDialog}
  maxWidth="md"
  fullWidth
  PaperProps={{
    sx: {
      backgroundColor: 'transparent', // make the Dialog paper background transparent
      boxShadow: 'none',              // remove shadow
      overflow: 'hidden',
    },
  }}
>
  <DialogContent
    sx={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 2,
      px: 0,
      py: 0,
      backgroundColor: 'transparent', // ensure content is transparent too
      overflow: 'hidden',
    }}
  >

    <IconButton
      onClick={handlePrev}
      sx={{
        backgroundColor: 'white',
        boxShadow: 2,
        '&:hover': { backgroundColor: 'grey.200' },
      }}
    >
      <ArrowBackIos />
    </IconButton>

    {openImageIndex !== null && (
      <Box sx={{ maxWidth: '90%', maxHeight: '80vh', overflow: 'hidden' }}>
        <img
          src={`data:image/png;base64,${screenshots[openImageIndex].imageData}`}
          alt="Expanded Screenshot"
          style={{ width: '100%', height: 'auto', borderRadius: 8 }}
        />
      </Box>
    )}

    <IconButton
      onClick={handleNext}
      sx={{
        backgroundColor: 'white',
        boxShadow: 2,
        '&:hover': { backgroundColor: 'grey.200' },
      }}
    >
      <ArrowForwardIos />
    </IconButton>
  </DialogContent>
</Dialog>

    </Box>
  )
}

export default ScreenShotsDetails
