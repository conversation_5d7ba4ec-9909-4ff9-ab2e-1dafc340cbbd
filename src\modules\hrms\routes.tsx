/**
 * HRMS Module Routes
 * 
 * This file defines all routes for the Human Resource Management System module.
 * Routes are organized by feature area with proper lazy loading and permissions.
 */

import React, { lazy, Suspense } from 'react';
import { Route, Switch, Redirect } from 'react-router-dom';
import { LoadingScreen } from 'modules/common/components';
import { PermissionRoute } from 'modules/common/components';

// Lazy load HRMS pages (these will be migrated from existing screens)
// Leave Management
const LeaveDashboard = lazy(() => import('./pages/LeaveDashboard'));
const Leaves = lazy(() => import('screens/Leave'));
const LeaveForm = lazy(() => import('screens/Leave/Form'));

// Leave Approval
const LeaveApproval = lazy(() => import('screens/LeaveApproval/Approval'));

// Leave Configuration
const LeaveConfiguration = lazy(() => import('screens/LeaveConfiguration/LeaveConfiguration'));

// Leave Calendar
const LeaveCalendar = lazy(() => import('screens/LeaveCalendar/LeaveCalendar'));

// Leave Reporting
const LeaveReport = lazy(() => import('screens/LeaveReport/LeaveReport'));

// Attendance Management
const Attendance = lazy(() => import('screens/Attendance'));
const AttendanceForm = lazy(() => import('screens/Attendance/Form'));

// Loading component
const Loader = () => <LoadingScreen />;

/**
 * HRMS Routes Component
 * 
 * Handles all routing within the Human Resource Management System module.
 */
export default function HRMSRoutes() {
  return (
    <Suspense fallback={<Loader />}>
      <Switch>
        {/* HRMS Main Routes */}
        <Route exact path="/app/hrms">
          <Redirect to="/app/hrms/dashboard" />
        </Route>

        <Route exact path="/app/hrms/dashboard">
          <LeaveDashboard />
        </Route>

        {/* Leave Management Routes */}
        <PermissionRoute
          exact
          path="/app/hrms/leaves"
          component={Leaves}
          permission={{ feat: 'leave', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/hrms/leaves/create"
          component={LeaveForm}
          permission={{ feat: 'leave', act: 'create' }}
        />

        <PermissionRoute
          exact
          path="/app/hrms/leaves/update/:id"
          component={LeaveForm}
          permission={{ feat: 'leave', act: 'update' }}
        />

        {/* Leave Approval Routes */}
        <PermissionRoute
          exact
          path="/app/hrms/leaves/approval"
          component={LeaveApproval}
          permission={{ feat: 'approve', act: 'read' }}
        />

        {/* Leave Configuration Routes */}
        <PermissionRoute
          exact
          path="/app/hrms/leaves/configuration"
          component={LeaveConfiguration}
          permission={{ feat: 'configuration', act: 'read' }}
        />

        {/* Leave Calendar Routes */}
        <PermissionRoute
          exact
          path="/app/hrms/leaves/calendar"
          component={LeaveCalendar}
          permission={{ feat: 'calendar', act: 'read' }}
        />

        {/* Leave Reporting Routes */}
        <PermissionRoute
          exact
          path="/app/hrms/leaves/report"
          component={LeaveReport}
          permission={{ feat: 'leavereport', act: 'read' }}
        />

        {/* Attendance Management Routes */}
        <PermissionRoute
          exact
          path="/app/hrms/attendance"
          component={Attendance}
          permission={{ feat: 'attendance', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/hrms/attendance/create"
          component={AttendanceForm}
          permission={{ feat: 'attendance', act: 'create' }}
        />

        <PermissionRoute
          exact
          path="/app/hrms/attendance/update/:id"
          component={AttendanceForm}
          permission={{ feat: 'attendance', act: 'update' }}
        />

        {/* User-specific Leave Routes */}
        <PermissionRoute
          exact
          path="/app/hrms/user/leaves"
          component={Leaves}
          permission={{ feat: 'leave', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/hrms/user/leaves/create"
          component={LeaveForm}
          permission={{ feat: 'leave', act: 'create' }}
        />

        <PermissionRoute
          exact
          path="/app/hrms/user/leaves/update/:id"
          component={LeaveForm}
          permission={{ feat: 'leave', act: 'update' }}
        />

        {/* HR Analytics and Reports */}
        <Route exact path="/app/hrms/analytics">
          {/* TODO: Create HR Analytics Dashboard */}
          <div>HR Analytics Dashboard - Coming Soon</div>
        </Route>

        <Route exact path="/app/hrms/reports">
          {/* TODO: Create HR Reports Dashboard */}
          <div>HR Reports Dashboard - Coming Soon</div>
        </Route>

        {/* Holiday Management */}
        <Route exact path="/app/hrms/holidays">
          {/* TODO: Create Holiday Management */}
          <div>Holiday Management - Coming Soon</div>
        </Route>

        {/* Leave Types Management */}
        <Route exact path="/app/hrms/leave-types">
          {/* TODO: Create Leave Types Management */}
          <div>Leave Types Management - Coming Soon</div>
        </Route>

        {/* Leave Policies Management */}
        <Route exact path="/app/hrms/leave-policies">
          {/* TODO: Create Leave Policies Management */}
          <div>Leave Policies Management - Coming Soon</div>
        </Route>

        {/* Attendance Reports */}
        <Route exact path="/app/hrms/attendance/reports">
          {/* TODO: Create Attendance Reports */}
          <div>Attendance Reports - Coming Soon</div>
        </Route>

        {/* Default redirect for unmatched HRMS routes */}
        <Route>
          <Redirect to="/app/hrms/dashboard" />
        </Route>
      </Switch>
    </Suspense>
  );
}

// TODO: Create actual page components in /modules/hrms/pages/
// TODO: Migrate existing screens from /screens/Leave, /screens/Attendance, etc.
// TODO: Implement proper TypeScript interfaces for route props
// TODO: Add proper error boundaries for HRMS routes
// TODO: Implement proper breadcrumb generation
// TODO: Add route-level analytics for HRMS module
// TODO: Implement proper SEO meta tags for HRMS pages
// TODO: Add route-level loading states and skeleton screens
// TODO: Implement proper permission-based route guards
// TODO: Add route-level form validation and error handling
