import React, { useEffect, useState } from 'react';
import {
  Box, Card, Typography, List, ListItem, ListItemText,
  ListItemAvatar, Avatar, Divider, Tooltip
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { LeaveActions } from 'slices/actions';
import { LeaveSelector } from 'selectors/LeaveSelector';
import { PersonOff } from '@mui/icons-material';
import moment from 'moment';

function UserLeaveInfo() {
  const dispatch = useDispatch();
  const allLeaves = useSelector(LeaveSelector.getAllLeaves()) || [];
  const [todayLeaves, setTodayLeaves] = useState([]);
  const [upcomingLeaves, setUpcomingLeaves] = useState([]);

  useEffect(() => {
    dispatch(LeaveActions.getLeaves());
  }, [dispatch]);

  useEffect(() => {
    if (allLeaves.length > 0) {
      const today = moment().startOf('day');
      const tomorrow = moment().add(1, 'days').startOf('day');
      const currentMonth = moment().add(30, 'days').endOf('day');

      const todayList = [];
      const upcomingList = [];

      allLeaves.forEach((leave) => {
        const start = moment(leave.start).startOf('day');
        const end = moment(leave.end).startOf('day');

        // Today's Leave
        if (
          today.isSameOrAfter(start) &&
          today.isSameOrBefore(end) &&
          leave.status === 1
        ) {
          todayList.push(leave);
        }

        // Future leave within next 7 days (excluding today)
        else if (
          leave.status === 1 &&
          (
            (start.isSameOrAfter(tomorrow) && start.isSameOrBefore(currentMonth)) ||
            (end.isSameOrAfter(tomorrow) && end.isSameOrBefore(currentMonth))
          )
        ) {
          upcomingList.push(leave);
        }
      });

      setTodayLeaves(todayList);
      setUpcomingLeaves(upcomingList);
    }
  }, [allLeaves]);

  const getInitials = (name) => {
    return name ? name.charAt(0).toUpperCase() : 'U';
  };

  const renderLeaveList = (leaves) => (
    <List>
      {leaves.map((leave, index) => (
        <React.Fragment key={leave._id || index}>
          <ListItem alignItems="flex-start">
            <ListItemAvatar>
              <Tooltip title={leave.userName || 'Employee'}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  {getInitials(leave.userName)}
                </Avatar>
              </Tooltip>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Typography fontWeight={600} variant="body1">
                  {leave.userName || 'Employee'}
                </Typography>
              }
              secondary={
                <>
                  <Typography
                    component="span"
                    variant="body2"
                    color="text.primary"
                    sx={{ display: 'block', mt: 0.5 }}
                  >
                    {leave.type === 'halfday' ? 'Half Day' : 'Full Day'}
                    {leave.specifictype ? ` - ${leave.specifictype}` : ''}
                    {' • '}
                    {moment(leave.start).format('MMM D')} to {moment(leave.end).format('MMM D')}
                  </Typography>
                  {leave.description && (
                    <Typography
                      component="p"
                      variant="caption"
                      color="text.secondary"
                      sx={{ mt: 0.5 }}
                    >
                      {leave.description}
                    </Typography>
                  )}
                </>
              }
            />
          </ListItem>
          {index < leaves.length - 1 && <Divider variant="inset" component="li" />}
        </React.Fragment>
      ))}
    </List>
  );

  return (
    <Card sx={{ mt: 3, p: 3, borderRadius: 3, boxShadow: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <PersonOff color="primary" sx={{ mr: 1 }} />
        <Typography variant="h6" fontWeight={600}>
          Employee On Leave Today
        </Typography>
      </Box>

      {todayLeaves.length > 0 ? (
        renderLeaveList(todayLeaves)
      ) : (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No Employee on leave today
          </Typography>
        </Box>
      )}

      {/* Upcoming Leaves Section
      <Box sx={{ display: 'flex', alignItems: 'center', mt: 4, mb: 2 }}>
        <PersonOff color="secondary" sx={{ mr: 1 }} />
        <Typography variant="h6" fontWeight={600}>
          Upcoming Leaves
        </Typography>
      </Box>

      {upcomingLeaves.length > 0 ? (
        renderLeaveList(upcomingLeaves)
      ) : (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No upcoming leaves
          </Typography>
        </Box>
      )} */}
    </Card>
  );
}

export default UserLeaveInfo;
