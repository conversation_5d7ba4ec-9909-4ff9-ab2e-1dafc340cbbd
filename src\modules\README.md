# Modules Directory

This directory contains the modular architecture for the Meraki Frontend application, following SOLID principles and MVC patterns.

## Architecture Overview

The application is organized into domain-specific modules with clear separation of concerns:

### Core Modules
- **PMS (Project Management System)** - `/pms`
- **<PERSON><PERSON><PERSON> (Human Resource Management System)** - `/hrms` 
- **<PERSON><PERSON> (Employee Management System)** - `/ems`
- **Common** - `/common` - Shared utilities and components

### Module Structure
Each module follows a consistent structure:
```
/module-name
  /components     # UI components specific to this module
  /pages         # Screen/route components for this module
  /services      # API calls and business logic
  /store         # Redux slices/context for state management
  /utils         # Helper functions specific to this module
  /types         # TypeScript type definitions
  /constants     # Module-specific constants
  README.md      # Module documentation
```

### Common Module Structure
```
/common
  /components    # Reusable UI components across all modules
  /layouts       # Layout wrappers (DashboardLayout, AuthLayout, etc.)
  /hooks         # Shared React hooks
  /services      # Shared API/auth/axios configuration
  /constants     # Global constants and enums
  /utils         # General helper functions
  /types         # Shared TypeScript types
  /store         # Global state management
```

## Design Principles

### SOLID Principles Applied
1. **Single Responsibility** - Each module handles one domain
2. **Open/Closed** - Modules are open for extension, closed for modification
3. **Liskov Substitution** - Components can be substituted without breaking functionality
4. **Interface Segregation** - Modules depend only on interfaces they use
5. **Dependency Inversion** - High-level modules don't depend on low-level modules

### MVC Pattern
- **Model** - `/store` and `/services` handle data and business logic
- **View** - `/components` and `/pages` handle presentation
- **Controller** - Custom hooks and service layers handle user interactions

## Migration Strategy

1. Keep existing code in `/src` intact
2. Gradually move components to appropriate modules
3. Update imports as components are migrated
4. Test each migration step thoroughly
5. Remove old files only after successful migration

## Usage Guidelines

1. Import from modules using absolute paths: `modules/pms/components/ProjectCard`
2. Keep module dependencies minimal and explicit
3. Use common module for shared functionality
4. Follow TypeScript conventions for type safety
5. Document any cross-module dependencies
