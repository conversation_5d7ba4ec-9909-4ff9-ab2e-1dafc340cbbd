# PMS (Project Management System) Module

This module handles all project management functionality including projects, tasks, sprints, timelines, and client management.

## Features Covered
- Project creation and management
- Task assignment and tracking
- Sprint planning and execution
- Timeline management
- Client relationship management
- Time tracking and timesheets
- Project reporting and analytics

## Components
- Project cards and lists
- Task management interfaces
- Sprint boards and planning tools
- Timeline visualizations
- Client management forms

## Pages
- Project dashboard
- Project list and overview
- Task management screens
- Sprint planning and tracking
- Timeline views
- Client management
- Timesheet management

## Services
- Project API calls
- Task management services
- Sprint management services
- Timeline services
- Client services
- Timesheet services

## Store
- Project state management
- Task state management
- Sprint state management
- Timeline state management
- Client state management

## Migration Notes
Current screens to migrate:
- `/screens/Product/*` → `/modules/pms/pages/`
- `/screens/Client/*` → `/modules/pms/pages/`
- `/screens/Sprints/*` → `/modules/pms/pages/`
- `/screens/Timeline/*` → `/modules/pms/pages/`
- `/screens/ActivityTimeline/*` → `/modules/pms/pages/`

Current services to migrate:
- `ProductService.js` → `modules/pms/services/`
- `ClientService.js` → `modules/pms/services/`
- `SprintService.js` → `modules/pms/services/`
- `TimelineService.js` → `modules/pms/services/`
- `ActivityService.js` → `modules/pms/services/`
