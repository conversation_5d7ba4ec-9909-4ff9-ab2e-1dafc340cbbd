import React, { Suspense, useEffect, lazy } from "react";
import { Box, CircularProgress } from "@mui/material";
import { useSelector } from "react-redux";
import { UserSelector } from "selectors";
import { useHistory, useLocation } from "react-router-dom";
import ROLES from "constants/role";

// Lazy load dashboard components
const AdminDashboard = lazy(() => import('./AdminDashboard'));
const UserDashboard = lazy(() => import('./UserDashboard'));

// Loading component
const LoadingFallback = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
    <CircularProgress />
  </Box>
);

export default function Dashboard() {
    const history = useHistory();
    const location = useLocation();
    const profile = useSelector(UserSelector.profile()) || {};

    // Check if user is admin or HR
    const roles = profile?.role || [];
    const isAdmin = roles.length > 0 && roles.includes(ROLES.admin.id);
    const isHR = roles.length > 0 && roles.includes(ROLES.humanresource.id);

    // Check current path and redirect if needed
    useEffect(() => {
        if (!profile) {
            return; // Wait for profile to load
        }

        const currentPath = location.pathname;

        // If we're on the main dashboard route, redirect based on role
        if (currentPath === '/app/dashboard') {
            if (isAdmin || isHR) {
                history.replace('/app/admin-dashboard');
            } else {
                history.replace('/app/user-dashboard');
            }
        }

        // Handle direct access to admin dashboard by non-admin users
        if (currentPath === '/app/admin-dashboard' && !isAdmin && !isHR) {
            history.replace('/app/user-dashboard');
        }

        // Handle direct access to user dashboard by admin users
        if (currentPath === '/app/user-dashboard' && (isAdmin || isHR)) {
            history.replace('/app/admin-dashboard');
        }
    }, [location.pathname, isAdmin, isHR, history, profile]);

    // Render different content based on the route
    const renderDashboardContent = () => {
        const currentPath = location.pathname;

        // Admin dashboard
        if (currentPath === '/app/admin-dashboard') {
            return (
                <Suspense fallback={<LoadingFallback />}>
                    <AdminDashboard />
                </Suspense>
            );
        }

        // User dashboard
        if (currentPath === '/app/user-dashboard') {
            return (
                <Suspense fallback={<LoadingFallback />}>
                    <UserDashboard />
                </Suspense>
            );
        }

        // Default dashboard (should redirect, but just in case)
        return <LoadingFallback />;
    };

    return renderDashboardContent();
}