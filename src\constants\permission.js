export const actions = {
    readAll: 'read_all',
    readSome: 'read_some',
    readSelf: 'read_self',
    read: 'read',
    create: 'create',
    update: 'update',
    delete: 'delete'
};

export const features = {
    // Core features
    user: 'User',
    attendance: 'Attendance',
    expense: 'Expense',
    leave: 'Leave',
    department: 'Department',
    designation: 'Designation',
    report: 'Report',
    setting: 'Setting',

    // Leave management features
    calendar: 'Calendar',
    configuration: 'Configuration',
    approve: 'Approve',
    leavereport: 'Leave Report',

    // Timeline features
    overview: 'Overview',
    timerequest: 'Time Request',
    taskrequest: 'Task Request',
    workschedule: 'Work Schedule',

    // Project features
    projects: 'Projects',
    projectlist: 'Project List',
    projectoverview: 'Project Overview',
    projecttimesheet: 'Project Timesheet',
    client: 'Client',

    // Task features
    tasks: 'Tasks',
    mytasks: 'Tasks',
    tasknote: 'Task Note',

    // Timeline features
    timeline: 'Timeline',
    usertimeline: 'Timeline',
    userleave: 'Leave',

    // Dashboard features
    dashboard: 'Dashboard',
    userdashboard: 'Dashboard',
    admindashboard: 'Dashboard',
    
    // Sprint features
    sprint: 'Sprint',
    sprintlist: 'Sprint List',
    usersprint: 'Sprint'
};