/**
 * PMS Module Routes
 * 
 * This file defines all routes for the Project Management System module.
 * Routes are organized by feature area with proper lazy loading and permissions.
 */

import React, { lazy, Suspense } from 'react';
import { Route, Switch, Redirect } from 'react-router-dom';
import { LoadingScreen } from 'modules/common/components';
import { PermissionRoute } from 'modules/common/components';

// Lazy load PMS pages (these will be migrated from existing screens)
// Project Management
const ProjectDashboard = lazy(() => import('./pages/ProjectDashboard'));
const ProjectList = lazy(() => import('screens/Product/ProductList'));
const ProjectOverview = lazy(() => import('screens/Product/ProductOverview'));
const ProjectTimesheet = lazy(() => import('screens/Product/ProductTimesheet'));

// Task Management
const TaskList = lazy(() => import('screens/Product/Tasklist'));
const TaskHistory = lazy(() => import('screens/Product/TaskHistoryAdmin'));
const TaskWithNote = lazy(() => import('screens/Product/Staff/TaskListWithNote'));

// Client Management
const ClientList = lazy(() => import('screens/Client/Client'));

// Sprint Management
const SprintPage = lazy(() => import('screens/Sprints/pages/SprintPage'));
const UserSprintPage = lazy(() => import('screens/Sprints/pages/UserSprintPage'));
const SprintTasksPage = lazy(() => import('screens/Sprints/pages/SprintTasksPage'));

// Timeline Management
const Timeline = lazy(() => import('screens/Timeline'));
const TimelineOverview = lazy(() => import('screens/ActivityTimeline/Overview/Overview'));
const TimeRequest = lazy(() => import('screens/ActivityTimeline/TimeRequest/TimeRequest'));
const TaskRequest = lazy(() => import('screens/ActivityTimeline/TaskRequest/TaskRequest'));
const WorkSchedule = lazy(() => import('screens/ActivityTimeline/WorkSchedule/WorkSchedule'));

// Loading component
const Loader = () => <LoadingScreen />;

/**
 * PMS Routes Component
 * 
 * Handles all routing within the Project Management System module.
 */
export default function PMSRoutes() {
  return (
    <Suspense fallback={<Loader />}>
      <Switch>
        {/* Project Management Routes */}
        <Route exact path="/app/pms">
          <Redirect to="/app/pms/dashboard" />
        </Route>

        <Route exact path="/app/pms/dashboard">
          <ProjectDashboard />
        </Route>

        <PermissionRoute
          exact
          path="/app/pms/projects"
          component={ProjectList}
          permission={{ feat: 'projectlist', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/projects/overview"
          component={ProjectOverview}
          permission={{ feat: 'projectoverview', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/projects/timesheet"
          component={ProjectTimesheet}
          permission={{ feat: 'projecttimesheet', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/projects/update/:data"
          component={TaskHistory}
          permission={{ feat: 'projects', act: 'update' }}
        />

        {/* Task Management Routes */}
        <PermissionRoute
          exact
          path="/app/pms/tasks"
          component={TaskList}
          permission={{ feat: 'My Tasks', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/tasks/note/:data"
          component={TaskWithNote}
          permission={{ feat: 'tasknote', act: 'read' }}
        />

        {/* Client Management Routes */}
        <PermissionRoute
          exact
          path="/app/pms/clients"
          component={ClientList}
          permission={{ feat: 'client', act: 'read' }}
        />

        {/* Sprint Management Routes */}
        <PermissionRoute
          exact
          path="/app/pms/sprints"
          component={SprintPage}
          permission={{ feat: 'Sprint', act: 'read_all' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/sprints/form"
          component={SprintPage}
          permission={{ feat: 'Sprint', act: 'create' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/sprints/form/:id"
          component={SprintPage}
          permission={{ feat: 'Sprint', act: 'update' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/sprints/:sprintId/tasks"
          component={SprintTasksPage}
          permission={{ feat: 'Sprint', act: 'read_all' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/sprints/user"
          component={UserSprintPage}
          permission={{ feat: 'Sprint', act: 'read_self' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/sprints/user/:sprintId/tasks"
          component={SprintTasksPage}
          permission={{ feat: 'Sprint', act: 'read_self' }}
        />

        {/* Timeline Management Routes */}
        <PermissionRoute
          exact
          path="/app/pms/timeline"
          component={Timeline}
          permission={{ feat: 'timeline', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/timeline/overview"
          component={TimelineOverview}
          permission={{ feat: 'overview', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/timeline/request"
          component={TimeRequest}
          permission={{ feat: 'timerequest', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/timeline/taskrequest"
          component={TaskRequest}
          permission={{ feat: 'taskrequest', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/timeline/workschedule"
          component={WorkSchedule}
          permission={{ feat: 'workschedule', act: 'read' }}
        />

        {/* Staff/User specific routes */}
        <PermissionRoute
          exact
          path="/app/pms/user/projects"
          component={lazy(() => import('screens/Product/Staff/ProductListStaff'))}
          permission={{ feat: 'Projects', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/user/projects/overview"
          component={ProjectOverview}
          permission={{ feat: 'projectoverview', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/user/projects/timesheet"
          component={ProjectTimesheet}
          permission={{ feat: 'projecttimesheet', act: 'read' }}
        />

        <PermissionRoute
          exact
          path="/app/pms/user/timeline"
          component={Timeline}
          permission={{ feat: 'Timeline', act: 'read' }}
        />

        {/* Default redirect for unmatched PMS routes */}
        <Route>
          <Redirect to="/app/pms/dashboard" />
        </Route>
      </Switch>
    </Suspense>
  );
}

// TODO: Create actual page components in /modules/pms/pages/
// TODO: Migrate existing screens from /screens/Product, /screens/Client, etc.
// TODO: Implement proper TypeScript interfaces for route props
// TODO: Add proper error boundaries for PMS routes
// TODO: Implement proper breadcrumb generation
// TODO: Add route-level analytics for PMS module
// TODO: Implement proper SEO meta tags for PMS pages
// TODO: Add route-level loading states and skeleton screens
