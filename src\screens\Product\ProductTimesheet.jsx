import React, { useState, useEffect, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Typography,
  Box,
  Button,
} from "@mui/material";
import { KeyboardArrowDown, KeyboardArrowUp } from "@mui/icons-material";
import { ProductActions} from "slices/actions";
import { ProductSelector, UserSelector } from "selectors";
import WeeklyPicker from "./WeeklyPicker";
import TimesheetFilters from "./TimesheetFilters";
import dayjs from "dayjs";

const ProductTimesheet = () => {
  const dispatch = useDispatch();

  // Fetch products and user profile
  const products = useSelector(ProductSelector.getProducts());
  const profile = useSelector(UserSelector.profile()) || {};

  // State to manage open/closed project views
  const [openProjects, setOpenProjects] = useState({});

  // State to store selected week range
  const [selectedWeek, setSelectedWeek] = useState({
    startOfWeek: "",
    endOfWeek: "",
    selectedDate: "",
  });

  const [filters, setFilters] = useState({
    project: "",
    user: "", // ✅ Match with filtering logic
    status: "",
    type: "",
    tag: "",
  });

  // State to store week days dynamically
  const [weekDays, setWeekDays] = useState([]);

  // Fetch all products when component mounts
  useEffect(() => {
    dispatch(ProductActions.getProducts());
  }, [dispatch]);

  // Update weekDays whenever selectedWeek changes
  useEffect(() => {
    if (selectedWeek.startOfWeek) {
      const start = dayjs(selectedWeek.startOfWeek);
      setWeekDays(Array.from({ length: 7 }, (_, i) => start.add(i, "day")));
    }
  }, [selectedWeek.startOfWeek]);

  const calculateTotal = (tasks, key) =>
    tasks.reduce((sum, task) => sum + (task[key] || 0), 0);

  console.log("Filters:", filters);

  const filteredProducts = useMemo(() => {
    return products.map((product) => {
        const matchesProject =
          !filters.project || product._id === filters.project;
        const matchesPriority =
          !filters.tag || product.priority === filters.tag;
        const matchesStatus =
          !filters.status || product.status === filters.status;
        const matchesUsers =
          !filters.user ||
          (Array.isArray(product.members) &&
            product.members.includes(filters.user)); // ✅ Fix for user filtering

        return matchesProject &&
          matchesPriority &&
          matchesStatus &&
          matchesUsers? product: null;
      }).filter(Boolean); // ✅ Remove null values
  }, [products, filters]);

  const processedProducts = useMemo(() => {
    return filteredProducts.map((product) => {
      const totalHours = calculateTotal(product.taskArr, "totalHours");
      const totalSpent = calculateTotal(product.taskArr, "totalSpent");

      const dayWiseTotalHours = weekDays.reduce((acc, day) => {
        const dateKey = day.format("YYYY-MM-DD");
        acc[dateKey] = product.taskArr.reduce(
          (sum, task) => sum + (task.hours?.[dateKey] || 0),
          0
        );
        return acc;
      }, {});

      return { ...product, totalHours, totalSpent, dayWiseTotalHours };
    });
  }, [filteredProducts, weekDays]);

  // Toggle project expansion using product ID for stability
  const toggleProject = (productId) => {
    setOpenProjects((prev) => ({ ...prev, [productId]: !prev[productId] }));
  };

  // Convert decimal hours to time format (hh mm ss)
  const formatDecimalToTime = (decimalHours) => {
    if (!decimalHours || decimalHours <= 0) {
      return "--";
    }
    // Ensure we don't have negative values
    const safeHours = Math.max(0, decimalHours);
    const totalSeconds = Math.floor(safeHours * 3600);
    const hrs = Math.floor(totalSeconds / 3600);
    const mins = Math.floor((totalSeconds % 3600) / 60);
    return `${hrs}h ${mins}m`;
  };

  const handleFilterChange = (selectedFilters) => {
    setFilters(selectedFilters);
  };

  return (
    <>
      {/* Title */}
      <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
        Product Timesheet
      </Typography>

      {/* Header */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center", // Center alignment
          p: 1.5,
          backgroundColor: "#f8f9fa",
          borderRadius: 1,
          boxShadow: "0px 2px 4px rgba(0,0,0,0.1)",
          marginBottom: "10px",
          gap: 2, // Adds space between sections
          paddingBottom: "30px",
        }}
      >
        {/* Filters */}
        <Box sx={{ display: "flex", gap: 1 }}>
          <TimesheetFilters onFilter={handleFilterChange} projects={products} />
        </Box>

       
        <Box sx={{ display: "flex", gap: 1 }}>

          {/* Weekly Picker */}
          <WeeklyPicker onChange={(week) => setSelectedWeek(week)} />

        {/* Summary Button */}
          <Button
            variant="contained"
            sx={{
              backgroundColor: "#4CAF50", // Green color
              color: "white",
              textTransform: "none",
              fontWeight: "bold",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            📊 Summary
          </Button>

          {/*Export Button*/}
          <Button
            variant="contained"
            sx={{
              backgroundColor: "#3F51B5", // Blue color
              color: "white",
              textTransform: "none",
              fontWeight: "bold",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            ⬇️ Export
          </Button>

        </Box>
      </Box>

      {/* Timesheet Table */}
      <TableContainer component={Paper}>
        <Table sx={{ border: "1px solid #ddd" }}>
          {/* Table Head */}
          <TableHead>
            <TableRow>
              <TableCell
                sx={{ fontWeight: "bold", width: "300px", textAlign: "left" }}
              >
                Projects
              </TableCell>

              {/* Dynamically render week days */}
              {weekDays.map((day, index) => {
                const isToday = day.isSame(dayjs(), "day");
                const isCurrentWeek = dayjs().isBetween(
                  selectedWeek.startOfWeek,
                  selectedWeek.endOfWeek,
                  null,
                  "[]"
                );

                return (
                  <TableCell
                    key={index}
                    align="center"
                    sx={{
                      fontWeight: "bold",
                      backgroundColor:
                        isToday && isCurrentWeek ? "#1976d2" : "#f9f9f9",
                      color: isToday && isCurrentWeek ? "#fff" : "inherit",
                      borderBottom: "2px solid #ddd",
                    }}
                  >
                    {day.format("MMM D, ddd")}
                  </TableCell>
                );
              })}

              <TableCell
                sx={{ fontWeight: "bold", width: "120px", textAlign: "center" }}
              >
                Total Hours
              </TableCell>
              <TableCell
                sx={{ fontWeight: "bold", width: "120px", textAlign: "center" }}
              >
                Total Spent
              </TableCell>
            </TableRow>
          </TableHead>

          {/* Table Body */}
          <TableBody>
            {processedProducts.map((product) => (
              <React.Fragment key={product._id}>
                {/* Project Row */}
                <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                  <TableCell
                    sx={{
                      fontWeight: "bold",
                      textAlign: "left",
                      width: "250px", // Set a fixed width
                      whiteSpace: "nowrap", // Prevent text wrapping
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Show "..." if text is too long
                    }}
                  >
                    <IconButton
                      onClick={() => toggleProject(product._id)}
                      size="small"
                      sx={{ marginRight: 1 }} // Add spacing between arrow & text
                    >
                      {openProjects[product._id] ? (
                        <KeyboardArrowUp />
                      ) : (
                        <KeyboardArrowDown />
                      )}
                    </IconButton>
                    {product.productName}
                  </TableCell>

                  {/* Show total hours per day for this product */}
                  {weekDays.map((day, i) => (
                    <TableCell key={i} align="center">
                      {formatDecimalToTime(
                        product.dayWiseTotalHours[day.format("YYYY-MM-DD")] || 0
                      )}
                    </TableCell>
                  ))}

                  <TableCell sx={{ textAlign: "center" }}>
                    {formatDecimalToTime(product.totalHours)}
                  </TableCell>
                  <TableCell sx={{ textAlign: "center" }}>
                    {formatDecimalToTime(product.totalSpent)}
                  </TableCell>
                </TableRow>

                {/* Task Rows (Visible when project is expanded) */}
                {openProjects[product._id] &&
                  product.taskArr?.map((task) =>
                    (profile._id &&
                    (task.assignee?.includes(profile._id) ||
                      product.visibility ||
                      task.reporter === profile._id) ? (
                      <TableRow key={task._id}>
                        {/* Task Title */}
                        <TableCell sx={{ textAlign: "left", paddingLeft: 6 }}>
                          {task.taskTitle}
                        </TableCell>

                        {/* Render time for each day of the week */}
                        {weekDays.map((day, i) => (
                          <TableCell key={i} align="center">
                            {formatDecimalToTime(
                              task.hours?.[day.format("YYYY-MM-DD")] || 0
                            )}
                          </TableCell>
                        ))}

                        {/* Total Hours & Total Spent per task */}
                        <TableCell sx={{ textAlign: "center" }}>
                          {formatDecimalToTime(task.totalHours || 0)}
                        </TableCell>
                        <TableCell sx={{ textAlign: "center" }}>
                          {formatDecimalToTime(task.totalSpent || 0)}
                        </TableCell>
                      </TableRow>
                    ) : null)
                  )}

                {/* Grand Total */}
              </React.Fragment>
            ))}
          </TableBody>
          {/* Grand Total Row */}
          <TableRow sx={{ backgroundColor: "#e0e0e0", fontWeight: "bold" }}>
            <TableCell sx={{ textAlign: "left", fontWeight: "bold" }}>
              Grand Total
            </TableCell>

            {/* Calculate total hours for each day across all products */}
            {weekDays.map((day, i) => {
              const totalDayHours = processedProducts.reduce(
                (sum, product) =>
                  sum +
                  (product.dayWiseTotalHours[day.format("YYYY-MM-DD")] || 0),
                0
              );
              return (
                <TableCell key={i} align="center">
                  {formatDecimalToTime(totalDayHours)}
                </TableCell>
              );
            })}

            {/* Total Hours Column */}
            <TableCell sx={{ textAlign: "center" }}>
              {formatDecimalToTime(
                processedProducts.reduce(
                  (sum, product) => sum + product.totalHours,
                  0
                )
              )}
            </TableCell>

            {/* Total Spent Column */}
            <TableCell sx={{ textAlign: "center" }}>
              {formatDecimalToTime(
                processedProducts.reduce(
                  (sum, product) => sum + product.totalSpent,
                  0
                )
              )}
            </TableCell>
          </TableRow>
        </Table>
      </TableContainer>
    </>
  );
};  

export default ProductTimesheet;
