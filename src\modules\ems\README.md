# EMS (Employee Management System) Module

This module handles all employee management functionality including user management, department management, designation management, and organizational structure.

## Features Covered
- Employee/User management and profiles
- Department management and hierarchy
- Designation and role management
- Employee onboarding and offboarding
- Organizational structure management
- Employee reporting and analytics

## Components
- Employee cards and lists
- Department management interfaces
- Designation management forms
- Employee profile components
- Organizational chart components

## Pages
- Employee dashboard
- Employee list and management
- Employee profile and details
- Department management
- Designation management
- Employee onboarding
- Employee reports

## Services
- Employee/User management services
- Department management services
- Designation management services
- Employee reporting services

## Store
- Employee state management
- Department state management
- Designation state management
- Organizational structure state

## Migration Notes
Current screens to migrate:
- `/screens/User/*` → `/modules/ems/pages/`
- `/screens/Department/*` → `/modules/ems/pages/`
- `/screens/Designation/*` → `/modules/ems/pages/`
- `/screens/Profile/*` → `/modules/ems/pages/`

Current services to migrate:
- `UserService.js` → `modules/ems/services/`
- `DepartmentService.js` → `modules/ems/services/`
- `DesignationService.js` → `modules/ems/services/`

Current constants to migrate:
- `role.js` → `modules/ems/constants/`
- `gender.js` → `modules/ems/constants/`
