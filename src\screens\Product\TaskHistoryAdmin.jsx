import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  Box,
  Card,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  Pagination,
  TableRow,
  Hidden,
  Typography,
  IconButton,
  CircularProgress
} from "@mui/material";

import styled from "@emotion/styled";
import { DefaultSort } from 'constants/sort';
import { PlayCircle, StopCircle, Delete, Visibility, PauseCircle, CheckCircle } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { ProductSelector, UserSelector } from 'selectors';
import { useParams, useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import ProductHeader from './components/ProductHeader';
import { ProductActions, UserActions } from 'slices/actions';
import TaskInfoComponent from './components/TaskInfoComponent';
import { setGlobalTimerState, formatDecimalToTime } from '../../utils/timerUtils';
import socketService from "utils/socketService";
import { backContext } from 'screens/Dashboard/components/Backgroundprovider';


// Simple timer formatting
const formatTimer = (seconds) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};


// const initSocket = () => {
//   if (!socket) {
//     socket = io('http://localhost:10000', {
//       transports: ['websocket'],
//       reconnection: true,
//     });

//     socket.on('connect', () => {
//       console.log('🔌 Admin Panel Socket connected');
//     });
//   }
//   return socket;
// };

const FilterBox = styled(Box)(() => ({
  width: "100%",
  marginTop: 30,
  marginBottom: 20,
  display: "flex",
  justifyContent: "space-between",
}));

function TaskHistoryAdmin() {
  const { data } = useParams();
  const tasks = useSelector(ProductSelector.getTasks()) || [];
  const projects = useSelector(ProductSelector.getProducts()) || [];
  const pagination = useSelector(ProductSelector.getTaskPagination()) || {};
  const users = useSelector(UserSelector.getUsers()) || [];
  const profile = useSelector(UserSelector.profile());
  let {runningTaskId,runningTaskProjectId,isRunning,timerSeconds,setRunningTaskId,setRunningTaskProjectId,setTimerSeconds,setIsRunning,didByUser,op,setOp,setDidByUser } = useContext(backContext);
  const dispatch = useDispatch();
  const history = useHistory();

  const [product, setProduct] = useState([]);
  const [currentTask, setCurrentTask] = useState(null);
  const [showTaskInfo, setShowTaskInfo] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState({
    sort: DefaultSort.newest.value,
    page: 1,
  });

  useEffect(() => {
    if (!projects.length) { dispatch(ProductActions.getProducts()) }
    if (!users.length) { dispatch(UserActions.getUsers()) }
  }, []);

  useEffect(() => {
    if (data && projects?.length > 0) {
      const foundProduct = projects.filter((element) => element._id === data);
      setProduct(foundProduct);
    }
  }, [data, projects]);

  useEffect(() => {
    if (data) {
      setLoading(true);
      dispatch(ProductActions.getTasks({ projectid: data, filter }));
      setTimeout(() => setLoading(false), 2000);
    }
  }, [data, filter]);

  useEffect(() => {
    if (tasks.length > 0 && product.length > 0) {
      const updatedProduct = { ...product[0], taskArr: tasks };
      setProduct([updatedProduct]);
    }
  }, [tasks]);

  const handleChangePagination = (e, val) => {
    setFilter((prev) => ({ ...prev, page: val }));
  };


  // Simplified start handler - following Tasklist pattern
  const handleStart = async (taskId) => {
    try {
      // Check if another task is already running
      if (runningTaskId && runningTaskId !== taskId && isRunning) {
        // alert("Stop current task first!");
        // return;
         console.log(" First pause the task ",runningTaskId,runningTaskProjectId,taskId)
          await handlePause(runningTaskId)
      }
      
      const projectId = product[0]?._id;
      if (!projectId) {
        console.error("Project ID not found");
        return;
      }
      
      const today = new Date().toISOString().split("T")[0];
      dispatch(ProductActions.startTask({ taskId, projectId, date: today }));
      
      // Start timer - simple like Tasklist
      setRunningTaskId(taskId);
      setRunningTaskProjectId(projectId);
      setTimerSeconds(0);
      setIsRunning(true);

      const timerData = {
        taskId,
        projectId,
        seconds: 0,
        isRunning: true,
        userId: profile._id,
        didByUser: "yes",
        op:"start"
      };

      setOp(timerData.op);
      setDidByUser(timerData.didByUser);

      setGlobalTimerState(timerData);
       localStorage.setItem("currentTask",JSON.stringify({taskId:taskId,projectId:projectId,taskStatus:"Start"}))
      // Emit to desktop
      if (socketService) {
        socketService.emit('timer-sync', timerData);
      }
      
    } catch (error) {
      console.error("Error starting task:", error);
    }
  };

  // Simplified stop handler - following Tasklist pattern
  const handleStop = (taskId) => {
    try {
      const projectId = product[0]?._id;
      if (!projectId) {
        console.error("Project ID not found");
        return;
      }
      
      dispatch(ProductActions.stopTask({ 
        taskId, 
        projectId,
        elapsedTime: timerSeconds,
        date: new Date().toISOString().split("T")[0]
      }));
      
      // Stop timer - simple like Tasklist
      setRunningTaskId(null);
      setRunningTaskProjectId(null);
      setTimerSeconds(0);
      setIsRunning(false);
      setGlobalTimerState(null);

      localStorage.removeItem("currentTask")
      // Emit to desktop
      if (socketService) {
        socketService.emit('timer-sync', {
          op:"stop",
          didByUser:"yes"
        });
      }
      setOp("stop");
      setDidByUser("yes");
      

    } catch (error) {
      console.error("Error stopping task:", error);
    }
  };
  
  // Simplified pause handler - following Tasklist pattern
  const handlePause = async (taskId) => {
    try {
      if (runningTaskId !== taskId) { return }

      const projectId = product[0]?._id;
      if (!projectId) {
        console.error("Project ID not found");
        return;
      }

      const today = new Date().toISOString().split("T")[0];

      dispatch(ProductActions.pauseTask({ 
        taskId, 
        projectId,
        elapsedTime: timerSeconds,
        pauseTime: new Date().toISOString(),
        date: today,
        startTime: new Date().toISOString()
      }));

      // Pause timer - reset to 00:00:00 like Tasklist
      setIsRunning(false);
      setTimerSeconds(0);

      const timerData = {
        taskId,
        projectId,
        seconds: 0,
        isRunning: false,
        userId: profile._id,
        didByUser: "yes",
        op:"pause"
      };
      setOp(timerData.op);
      setDidByUser(timerData.didByUser);

      setGlobalTimerState(timerData);

      // Emit to desktop
      if (socketService) {
        socketService.emit('timer-sync', timerData);
      }


    } catch (error) {
      console.error("Error pausing task:", error);
    }
  };

  const handleDelete = (taskId) => {
    const projectId = product[0]?._id;
    if (!projectId) {
      console.error("Project ID not found");
      return;
    }
    
    dispatch(ProductActions.deleteTask({ 
      taskId, 
      projectId
    }));

  };

  const handleView = (task) => {
    setCurrentTask(task);
    setShowTaskInfo(true);
  };

  const closeTaskInfo = () => {
    setShowTaskInfo(false);
    setCurrentTask(null);
  };

  const tasksArr = product[0]?.taskArr || [];
  const filteredTasks = tasksArr.filter(
    (task) =>
      task.assignee.includes(profile?._id) || task.reporter === profile?._id
  );

  return (
    <>
      {showTaskInfo && currentTask && (
        <TaskInfoComponent
          data={currentTask}
          productId={product[0]?._id}
          taskInfoController={closeTaskInfo}
        />
      )}

      <Card style={{ overflow: "scroll" }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            {product[0]?.productName || "Loading Product..."}
          </Typography>

 
        </Box>

        <FilterBox>
          <Grid container spacing={10} justifyContent="space-between">
            <Grid item lg={11} sm={12} xs={12}>
              <ProductHeader product={product} />
            </Grid>
          </Grid>
        </FilterBox>

        <Box>
          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Table>
                <TableHead>
                  <TableRow>
                    <Hidden smDown>
                      <TableCell align="center">Task Name</TableCell>
                      <TableCell align="center">Assignee</TableCell>
                      <TableCell align="center">Status</TableCell>
                      <TableCell align="center">Timer</TableCell>
                      <TableCell align="center">Total Spent</TableCell>
                      <TableCell align="center">Action</TableCell>
                    </Hidden>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTasks.length > 0 ? (
                    filteredTasks.map((task, index) => {
                      // Simplified UI logic like Tasklist
                      const isCurrentTask = runningTaskId === task._id;
                      const showPause = isCurrentTask && isRunning;
                      
                      return (
                        <TableRow key={index}>
                          <TableCell align="center">{task.taskTitle}</TableCell>
                          <TableCell align="center">
                            {task.assignee.map((assigneeId) => {
                                const user = users.find((u) => u._id === assigneeId);
                                return user ? user.name : null;
                              }).filter((name) => name !== null).join(", ")}
                          </TableCell>
                          <TableCell align="center">
                            {task.taskStatus === "Completed" ? (
                              <IconButton color="success">
                                <CheckCircle />
                              </IconButton>
                            ) : (
                              <>
                                {/* Play / Pause Button - simplified like Tasklist */}
                                <IconButton
                                  onClick={ () => { showPause ? handlePause(task._id) : handleStart(task._id) } }
                                  color={showPause ? "warning" : "primary"}
                                  // disabled={isRunning && !isCurrentTask}
                                >
                                  
                                  {showPause ? <PauseCircle /> : <PlayCircle />}
                                </IconButton>

                                {/* Stop Button - simplified like Tasklist */}
                                <IconButton
                                  onClick={() => handleStop(task._id)}
                                  color="secondary"
                                  // disabled={!isCurrentTask && isRunning}
                                >
                                  <StopCircle />
                                </IconButton>
                              </>
                            )}
                          </TableCell>
                          <TableCell align="center">
                            {isCurrentTask ? formatTimer(timerSeconds) : "00:00:00"}
                          </TableCell>
                          <TableCell align="center">
                            {formatDecimalToTime(task.totalSpent)}
                          </TableCell>
                          <TableCell align="center">
                            <IconButton onClick={() => handleView(task)}>
                              <Visibility />
                            </IconButton>
                            <IconButton onClick={() => handleDelete(task._id)}>
                              <Delete />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        No Tasks Found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {pagination.pages > 0 && (
                <Pagination
                  sx={{ mt: 1 }}
                  page={filter.page}
                  count={pagination.pages}
                  onChange={handleChangePagination}
                />
              )}
            </>
          )}
        </Box>
      </Card>
    </>
  );
}

export default TaskHistoryAdmin;