import { createSlice } from "@reduxjs/toolkit";


export const TimelineSlice = createSlice({
    name: "Timeline",
    initialState: {
        timelineArr: [],
        timelineToday: {},
        pagination: {}
    },
    reducers: {
        createTimelineRequest: () => {},
        updateTimelineRequest: () => {},
        getTimelineRequests: () => {},
        getTimelineRequestsSuccess: (state,action) => {
         
            if(action.payload.length === 0) {
                state.timelineArr = []
                state.pagination = {}
            } else {
                state.timelineArr = action.payload.data
                state.pagination = action.payload.pagination
            }
        },
        getTimelineRequestByDate: () => {

        },
        getTimelineRequestsTodaySuccess: (state,action) => {
            state.timelineToday = action.payload.data
        },
        updateTaskTimelineRequest: () => {},
        deleteTaskTimelineRequest: () => {}

    }
});

export default TimelineSlice;