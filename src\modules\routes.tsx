/**
 * Modular Routes Configuration
 * 
 * This file defines the new modular routing structure for the application.
 * Routes are organized by modules (PMS, HRMS, EMS) with proper lazy loading.
 * 
 * Usage:
 * This file will replace the existing /src/routes.js once migration is complete.
 */

import React, { lazy, Suspense } from 'react';
import { Route, Switch, Redirect } from 'react-router-dom';
import { useSelector } from 'react-redux';

// Common Components
import { LoadingScreen } from 'modules/common/components';
import { DashboardLayout, AuthLayout } from 'modules/common/layouts';
import { PermissionRoute } from 'modules/common/components';

// Auth Components
const Login = lazy(() => import('screens/Auth/Login'));

// Dashboard
const Dashboard = lazy(() => import('screens/Dashboard'));

// Common Pages
const Profile = lazy(() => import('screens/Profile'));
const NotFound = lazy(() => import('modules/common/components/NotFound'));
const AccessDenied = lazy(() => import('modules/common/components/AccessDenied'));

// PMS Module Routes (Project Management System)
const PMSRoutes = lazy(() => import('./pms/routes'));

// HRMS Module Routes (Human Resource Management System)  
const HRMSRoutes = lazy(() => import('./hrms/routes'));

// EMS Module Routes (Employee Management System)
const EMSRoutes = lazy(() => import('./ems/routes'));

// Route Configuration Interface
interface RouteConfig {
  path: string;
  component: React.ComponentType<any>;
  permission?: {
    feat: string;
    act: string;
  };
  exact?: boolean;
  module?: 'pms' | 'hrms' | 'ems' | 'common';
}

// Core Application Routes (non-module specific)
const coreRoutes: RouteConfig[] = [
  {
    path: '/app/dashboard',
    component: Dashboard,
    exact: true,
    module: 'common'
  },
  {
    path: '/app/profile',
    component: Profile,
    exact: true,
    module: 'common'
  }
];

// Loading component for suspense fallback
const Loader = () => <LoadingScreen />;

/**
 * Main Routes Component
 * 
 * This component handles the routing for the entire application using the new modular structure.
 * It includes lazy loading, permission-based routing, and proper error boundaries.
 */
export default function ModularRoutes() {
  const profile = useSelector((state: any) => state.auth?.profile);

  return (
    <Suspense fallback={<Loader />}>
      <Switch>
        {/* Private routes that require authentication */}
        <Route path="/app">
          <DashboardLayout>
            <Suspense fallback={<Loader />}>
              <Switch>
                {/* Core Application Routes */}
                {coreRoutes.map((route, index) => (
                  <Route
                    key={index}
                    exact={route.exact}
                    path={route.path}
                    component={route.component}
                  />
                ))}

                {/* PMS Module Routes */}
                <Route path="/app/pms">
                  <PMSRoutes />
                </Route>

                {/* HRMS Module Routes */}
                <Route path="/app/hrms">
                  <HRMSRoutes />
                </Route>

                {/* EMS Module Routes */}
                <Route path="/app/ems">
                  <EMSRoutes />
                </Route>

                {/* Legacy route redirects for backward compatibility */}
                {/* Project Management redirects */}
                <Redirect from="/app/project/:path*" to="/app/pms/projects/:path*" />
                <Redirect from="/app/client" to="/app/pms/clients" />
                <Redirect from="/app/sprint" to="/app/pms/sprints" />
                <Redirect from="/app/timeline" to="/app/pms/timeline" />
                <Redirect from="/app/tasks" to="/app/pms/tasks" />

                {/* HR Management redirects */}
                <Redirect from="/app/leave" to="/app/hrms/leaves" />
                <Redirect from="/app/attendance" to="/app/hrms/attendance" />

                {/* Employee Management redirects */}
                <Redirect from="/app/user" to="/app/ems/employees" />
                <Redirect from="/app/department" to="/app/ems/departments" />
                <Redirect from="/app/designation" to="/app/ems/designations" />

                {/* Default redirect to dashboard */}
                <Redirect exact from="/app" to="/app/dashboard" />

                {/* 404 for unmatched routes */}
                <Route component={NotFound} />
              </Switch>
            </Suspense>
          </DashboardLayout>
        </Route>

        {/* Public routes */}
        <Route exact path="/">
          <AuthLayout>
            <Suspense fallback={<Loader />}>
              <Switch>
                <Route exact path="/" component={Login} />
              </Switch>
            </Suspense>
          </AuthLayout>
        </Route>

        {/* Access Denied route */}
        <Route path="/access-denied">
          <DashboardLayout>
            <AccessDenied />
          </DashboardLayout>
        </Route>

        {/* 404 Not Found route */}
        <Route path="/not-found">
          <DashboardLayout>
            <NotFound />
          </DashboardLayout>
        </Route>

        {/* Redirect to not-found if no route matches */}
        <Route>
          <DashboardLayout>
            <NotFound />
          </DashboardLayout>
        </Route>
      </Switch>
    </Suspense>
  );
}

/**
 * Route Guard Component
 * 
 * This component handles permission-based routing and authentication checks.
 */
interface RouteGuardProps {
  children: React.ReactNode;
  permission?: {
    feat: string;
    act: string;
  };
  requireAuth?: boolean;
}

export function RouteGuard({ children, permission, requireAuth = true }: RouteGuardProps) {
  const isAuthenticated = useSelector((state: any) => state.auth?.isAuthenticated);
  const userPermissions = useSelector((state: any) => state.auth?.permissions);

  // Check authentication
  if (requireAuth && !isAuthenticated) {
    return <Redirect to="/" />;
  }

  // Check permissions
  if (permission && userPermissions) {
    const hasPermission = userPermissions.some((perm: any) => 
      perm.feature === permission.feat && perm.action === permission.act
    );
    
    if (!hasPermission) {
      return <Redirect to="/access-denied" />;
    }
  }

  return <>{children}</>;
}

// TODO: Implement module-specific route files (pms/routes.tsx, hrms/routes.tsx, ems/routes.tsx)
// TODO: Add proper TypeScript interfaces for route configurations
// TODO: Implement proper error boundaries for route-level errors
// TODO: Add route-level analytics and tracking
// TODO: Implement proper SEO meta tags for each route
// TODO: Add route-level loading states and skeleton screens
// TODO: Implement proper breadcrumb generation from routes
// TODO: Add route-level permission caching for better performance
