import React, { useContext, useEffect, useState } from 'react';
import PropTypes from "prop-types";
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { useDispatch, useSelector } from 'react-redux';
import { ActivityActions } from 'slices/actions';
import { ActivitySelector } from 'selectors/ActivitySelector';
import { backContext } from './Backgroundprovider';

TodayGoal.propTypes = {
  title: PropTypes.string,
  task: PropTypes.string,
  onClose: PropTypes.func,
  onSubmit: PropTypes.func.isRequired, // Make this required
  required: PropTypes.bool,
};

function TodayGoal({ task, title, onClose, onSubmit, required = false }) {
  // const [todayActivity, setActivity] = useState([]);
  // const todayHistoryArr = useSelector(ActivitySelector.getActivityHistory()) || [];
  const { profile } = useContext(backContext);
  const activities = useSelector(ActivitySelector.getActivityHistory());
  // const dispatch = useDispatch();
  const [todaysGoal, setGoal] = useState("");
  const [open, setOpen] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // useEffect(() => {
  //   if (activities.length > 0) {
  //     setActivity([activities[activities.length - 1]]);
  //   }
  //   console.log("ACTIVITY ", profile);
  // }, [activities]);

  const handleClose = () => {
    if (required && !todaysGoal.trim()) {
      // Don't allow closing if goal is required and empty
      return;
    }
    
    setOpen(false);
    if (onClose && typeof onClose === 'function') {
      onClose();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
 
    // Validate required goal
    if (required && !todaysGoal.trim()) {
      alert("Please enter your today's goal before proceeding with check-in.");
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const goalData = {
        todaysGoal: todaysGoal.trim(),
        task,
        profile
      };

         
      // Call the onSubmit prop with goal data
      if (onSubmit && typeof onSubmit === 'function') {
        await onSubmit(goalData);
      }
      
      // Close the dialog
      setOpen(false);
      
      if (onClose && typeof onClose === 'function') {
        onClose();
      }
    } catch (error) {
      console.error('Error submitting goal:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isToday = (createdAt) => {
    const today = new Date();
    const createdDate = new Date(createdAt);
    return today.getFullYear() === createdDate.getFullYear() && 
           today.getMonth() === createdDate.getMonth() && 
           today.getDate() === createdDate.getDate();
  };

  // Simplified rendering logic
  const shouldShowDialog = activities.length === 0 || 
    (activities.length > 0 && !activities.some(activity => isToday(activity.createdAt)));

  if (!shouldShowDialog) {
    return null;
  }

  return (
    <Dialog
      open={open}
      maxWidth="md"
      onClose={required ? undefined : handleClose} // Prevent closing if required
      disableEscapeKeyDown={required} // Prevent ESC key if required
      disableAutoFocus={true}
      disableEnforceFocus={true}
      disableRestoreFocus={true}
      onBackdropClick={required ? () => {} : handleClose} // Prevent backdrop click closing if required
      PaperProps={{
        component: 'form',
        sx: { width: '600px', height: '300px' },
        onSubmit: handleSubmit
      }}
    >
      <DialogTitle>
        {title || "Today's Goals"}
        {required && <span style={{ color: 'red' }}> *</span>}
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          {required ? "Please write your goals for today before checking in. This is mandatory to start your activity tracking." : "Write your goals for today"}
        </DialogContentText>
        <TextField
          autoFocus
          required={required}
          margin="dense"
          id="todaysGoal"
          name="todaysGoal"
          label={`Goals ${required ? '*' : ''}`}
          type="text"
          fullWidth
          multiline
          rows={5}
          variant="standard"
          value={todaysGoal}
          onChange={(e) => setGoal(e.target.value)}
          error={required && !todaysGoal.trim()}
          helperText={required && !todaysGoal.trim() ? "Goal is required to proceed with check-in" : ""}
          disabled={isSubmitting}
        />
      </DialogContent>
      <DialogActions>
        {!required && (
          <Button onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
        )}
        <Button 
          type="submit" 
          variant="contained"
          disabled={isSubmitting || (required && !todaysGoal.trim())}
        >
          {isSubmitting ? 'Submitting...' : 'Submit & Check In'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default TodayGoal;