import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Box, 
  Button, 
  Typography, 
  Container, 
  Grid, 
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { SprintActions, ProductActions } from '../../../slices/actions';
import SprintList from '../components/SprintList';
import SprintForm from '../components/SprintForm';
import { getSprints, isSprintLoading } from '../../../selectors/SprintSelector';
import { ProductSelector } from '../../../selectors/ProductSelector';
import PageTitle from '../../../components/PageTitle';
import Can from '../../../utils/can';

/**
 * Sprint Management Page
 * 
 * This component provides a UI for managing sprints in the project.
 * Admins can see all sprints, while regular users see only their own.
 */
const SprintPage = () => {
  const dispatch = useDispatch();
  const sprints = useSelector(getSprints);
  const loading = useSelector(isSprintLoading);
  const products = useSelector(ProductSelector.getProducts());
  const [openForm, setOpenForm] = useState(false);
  const [selectedSprint, setSelectedSprint] = useState(null);
  

  // Get current user from Redux store
const currentUser = useSelector(state => state.user.profile);
const isAdmin = currentUser?.role?.includes('admin');
// Add this line:
const canViewAllSprints = isAdmin || Can('read_all', 'Sprint');


useEffect(() => {
  dispatch(ProductActions.getProducts());
  
  const timer = setTimeout(() => {
    if (canViewAllSprints) {
      // User with read_all permission sees all sprints
      dispatch(SprintActions.getSprints({}));
    } else {
      // User with read_self permission sees their sprints
      dispatch(SprintActions.getSprints({ 
        userId: currentUser?._id,
        includeAssigned: true  // Include sprints where user is assigned
      }));
    }
  }, 300);
  
  return () => clearTimeout(timer);
}, [dispatch, canViewAllSprints, currentUser]);



  // Handle form open/close
  const handleOpenForm = () => {
    setSelectedSprint(null);
    setOpenForm(true);
  };

  const handleCloseForm = () => {
    setOpenForm(false);
    setSelectedSprint(null);
  };

  // Handle sprint operations
  const handleCreateSprint = (sprintData) => {
    // Validate product selection for new sprints
    if (!sprintData.productId) {
      alert('Please select a product for the sprint');
      return;
    }
    
    // Add current user as creator
    const sprintWithUser = {
      ...sprintData,
      createdBy: currentUser?._id
    };
    
    dispatch(SprintActions.createSprint(sprintWithUser));
    handleCloseForm();
  };

  const handleUpdateSprint = (sprintData) => {
    const sprintId = selectedSprint.id || selectedSprint._id;
    if (!sprintId) {
      console.error("Sprint ID is undefined");
      return;
    }
    
    dispatch(SprintActions.updateSprint({
      ...sprintData,
      id: sprintId
    }));
    handleCloseForm();
  };

  const handleDeleteSprint = (sprintId) => {
    if (window.confirm('Are you sure you want to delete this sprint?')) {
      dispatch(SprintActions.deleteSprint(sprintId));
    }
  };

  const handleEditSprint = (sprint) => {
    setSelectedSprint(sprint);
    setOpenForm(true);
  };

  const handleStartSprint = (sprintId) => {
    dispatch(SprintActions.startSprint({ id: sprintId }));
  };

  const handleCompleteSprint = (sprintId) => {
    dispatch(SprintActions.completeSprint({ id: sprintId }));
  };


// Update permission checks
const isMySprintPage = window.location.pathname.includes('/user/sprint');
const canCreateSprint = Can('create', 'Sprint');  // If user has create permission on Sprint

// console.log("Can Create Sprint:", canCreateSprint, "| User:", currentUser);


  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
       <PageTitle title={canViewAllSprints ? "Sprint Management" : "My Sprints"} />

        
        <Paper sx={{ p: 2, mb: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
             <Typography variant="h6">
  {canViewAllSprints ? "All Sprints" : "My Sprints"}
</Typography>


            </Grid>
            <Grid item xs={12} md={6} sx={{ textAlign: 'right' }}>
              {canCreateSprint && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleOpenForm}
                >
                  New Sprint
                </Button>
              )}
            </Grid>
          </Grid>
        </Paper>

      <SprintList 
  sprints={sprints} 
  loading={loading}
  onEdit={handleEditSprint}
  onDelete={handleDeleteSprint}
  onStart={handleStartSprint}
  onComplete={handleCompleteSprint}
  isAdmin={canViewAllSprints}  // Change this to use canViewAllSprints instead of isAdmin
  currentUserId={currentUser?._id}
/>

      </Box>

      <Dialog open={openForm} onClose={handleCloseForm} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedSprint ? 'Edit Sprint' : 'Create New Sprint'}
        </DialogTitle>
        <DialogContent>
          <SprintForm 
            sprint={selectedSprint} 
            onSubmit={selectedSprint ? handleUpdateSprint : handleCreateSprint}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseForm} color="secondary">
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SprintPage;