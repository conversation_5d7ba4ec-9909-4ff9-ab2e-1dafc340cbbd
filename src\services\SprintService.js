

const API_URL = "http://localhost:10000/api";

/**
 * Get all sprints with optional filtering
 */
async function getSprints(filter = {}) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        // Build query string from filter if provided
        let url = `${API_URL}/sprint`;
        const queryParams = new URLSearchParams();
        
        if (filter.userId) {
            url = `${API_URL}/sprint/user/${filter.userId}`;
        } else if (filter.projectId) {
            url = `${API_URL}/sprint/project/${filter.projectId}`;
        }
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch sprints');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error fetching sprints:", error);
        throw error;
    }
}

/**
 * Get sprint details by ID including tasks
 */
async function getSprintById(sprintId) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        const response = await fetch(`${API_URL}/sprint/${sprintId}/details`, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch sprint details');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error fetching sprint details:", error);
        throw error;
    }
}

/**
 * Create a new sprint
 */
async function createSprint(sprintData) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        const response = await fetch(`${API_URL}/sprint/create`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(sprintData)
        });

        if (!response.ok) {
            throw new Error('Failed to create sprint');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error creating sprint:", error);
        throw error;
    }
}

/**
 * Update an existing sprint
 */
async function updateSprint(sprintId, sprintData) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        const response = await fetch(`${API_URL}/sprint/update/${sprintId}`, {
            method: 'PATCH',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(sprintData)
        });

        if (!response.ok) {
            throw new Error('Failed to update sprint');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error updating sprint:", error);
        throw error;
    }
}

/**
 * Delete a sprint
 */
async function deleteSprint(sprintId) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        const response = await fetch(`${API_URL}/sprint/delete/${sprintId}`, {
            method: 'DELETE',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to delete sprint');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error deleting sprint:", error);
        throw error;
    }
}

/**
 * Add a task to a sprint
 */
async function addTaskToSprint(data) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        const response = await fetch(`${API_URL}/sprint/add-task`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error('Failed to add task to sprint');
        }

        const responseData = await response.json();
        return responseData;
    } catch (error) {
        console.error("Error adding task to sprint:", error);
        throw error;
    }
}

/**
 * Start a sprint
 */
async function startSprint(sprintId) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        const response = await fetch(`${API_URL}/sprint/start/${sprintId}`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to start sprint');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error starting sprint:", error);
        throw error;
    }
}

/**
 * Complete a sprint
 */
async function completeSprint(sprintId) {
    const token = localStorage.getItem("merakihr-token");
    
    try {
        const response = await fetch(`${API_URL}/sprint/complete/${sprintId}`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to complete sprint');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error completing sprint:", error);
        throw error;
    }
}

export const SprintService = {
    getSprints,
    getSprintById,
    createSprint,
    updateSprint,
    deleteSprint,
    addTaskToSprint,
    startSprint,
    completeSprint
};