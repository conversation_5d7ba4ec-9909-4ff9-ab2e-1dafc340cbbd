import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { ProductSelector, UserSelector } from "selectors";
import { ProductActions, UserActions } from "slices/actions";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  FormControl,
  MenuItem,
  Select,
  Stack,
  List,
  ListItem,
  ListItemText,
} from "@mui/material";
import {
  AccessTime,
  Assignment,
  WorkOutline,
  PlaylistAddCheck,
  Search,
} from "@mui/icons-material";
import {
  PieChart, Pie, Cell, Tooltip, ResponsiveContainer, BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend
} from "recharts";
import { format, parseISO, isValid, subDays, eachDayOfInterval } from "date-fns";
import PropTypes from "prop-types";
import dayjs from "dayjs";
import DateRangeSelector from "../Product/DateRangeSelector"; // Adjust the import path as necessary

const OverviewCard = ({
  title,
  value1,
  value2,
  icon,
  subtitle1,
  subtitle2,
}) => (
  <Card
    sx={{
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      p: 2,
      height: "100px", // Uniform height
      minWidth: "250px", // Ensures consistent width
    }}
  >
    {icon}
    <CardContent sx={{ textAlign: "left", flex: 1 }}>
      <Typography variant="subtitle1" gutterBottom>
        {title}
      </Typography>

      {/* Subtitles & Values Row */}
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <Typography variant="body2" color="textSecondary">
            {subtitle1}
          </Typography>
          <Typography variant="body2" color="primary">
            {value1 ?? "--:--"}
          </Typography>
        </div>

        {subtitle2 && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Typography variant="body2" color="textSecondary">
              {subtitle2}
            </Typography>
            <Typography variant="body2" color="primary">
              {value2 ?? "--:--"}
            </Typography>
          </div>
        )}
      </div>
    </CardContent>
  </Card>
);

OverviewCard.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle1: PropTypes.string.isRequired,
  subtitle2: PropTypes.string,
  value1: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  value2: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  icon: PropTypes.element.isRequired,
};

const ProductOverview = () => {

  const dispatch = useDispatch();

  // Fetch products and user profile
  const products = useSelector(ProductSelector.getProducts()) || [];

  // Fetch users from Redux
  const users = useSelector(UserSelector.getUsers()) || [];

  const getUserName = (userId) => {
    console.log("Looking for user with ID:", userId);
    console.log("Available users:", users);

    if (!users || users.length === 0) {
      console.log("Users list is empty or undefined!");
      return `User ${userId}`;
    }

    const user = users.find((u) => u._id === userId);

    if (!user) {
      console.log(`User not found for ID: ${userId}`);
    } else {
      console.log(`User found: ${user.name}`);
    }

    return user ? user.name : `User ${userId}`;
  };

  const formatHours = (decimalHours) => {
    if (!decimalHours) {
      return "0 hr";
    }
    const hours = Math.floor(decimalHours);
    const minutes = Math.round((decimalHours - hours) * 60);
    return `${hours} hr ${minutes > 0 ? `${minutes} min` : ""}`.trim();
  };

  // Function to calculate total hours (Billable + Non-Billable)
  const calculateTotalProductHours = (product) => {
    if (!product?.taskArr?.length) {
      return 0;
    } // If no tasks, return 0
    return product.taskArr.
      reduce((totalHours, task) => {
        const taskHours = Object.values(task.hours || {}).reduce(
          (sum, h) => sum + h,
          0
        ); // Sum all hours from the task's hours map
        return totalHours + taskHours;
      }, 0).
      toFixed(2); // Ensure result has 2 decimal places
  };

  // Count Completed & Ongoing Projects
  const completedProjects = products.filter(p => p.status === "Completed").length;
  const ongoingProjects = products.filter(p => p.status === "Ongoing").length;
  const totalProjects = completedProjects + ongoingProjects;

  // Donut Chart Data
  const data = [
    { name: "Completed", value: completedProjects, color: "#6FCF97" }, // Green
    { name: "Ongoing", value: ongoingProjects, color: "#2F80ED" }, // Blue
  ];

  // 🔹 Helper Function: Checks if a date falls within the selected range
  const isWithinRange = (date, startDate, endDate) => {
    if (!date || !startDate || !endDate) { return false; }
    const taskDate = new Date(date).setHours(0, 0, 0, 0);
    const start = new Date(startDate).setHours(0, 0, 0, 0);
    const end = new Date(endDate).setHours(23, 59, 59, 999);
    return taskDate >= start && taskDate <= end;
  };

  const [selectedRange, setSelectedRange] = useState("Last 7 Days");
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [taskTotalHours, setTaskTotalHours] = useState(0);
  const [filteredData, setFilteredData] = useState([]);
  const [userTasks, setUserTasks] = useState([]);
  const [totalSpent, setTotalSpent] = useState(0);
  const [selectedProductId, setSelectedProductId] = useState("");
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [billableTaskRange, setBillableTaskRange] = useState({
    startDate: subDays(new Date(), 6),
    endDate: new Date(),
  });
  const [taskOverviewData, setTaskOverviewData] = useState([]);

  useEffect(() => {
    if (users.length === 0) {
      dispatch(UserActions.getUsers()); // Dispatch action to fetch users
    }
  }, [dispatch, users.length]);

  useEffect(() => {
    if (!products.length) {
      console.log("Fetching products...");
      dispatch(ProductActions.getProducts());
    }
  }, [dispatch, products.length]);

  useEffect(() => {
    if (products.length > 0 && !selectedProductId) {
      setSelectedProductId(products[0]._id);
    }
  }, [products, selectedProductId]);

  useEffect(() => {
    if (selectedProductId) {
      const productDetails = products.find((p) => p._id === selectedProductId);
      setSelectedProduct(productDetails);
    }
  }, [selectedProductId, products]);

  // 🔹 useEffect: Filter tasks when `selectedRange` or `selectedProduct` changes
  useEffect(() => {
    if (selectedProduct?.taskArr) {
      const filtered = selectedProduct.taskArr.filter((task) =>
        isWithinRange(task.endTime, selectedRange.startDate, selectedRange.endDate)
      );

      setFilteredTasks(filtered);

      // 🔹 Calculate Total Hours
      const totalHours = filtered.reduce((sum, task) => sum + (task.totalHours || 0), 0);
      setTaskTotalHours(totalHours);

      // 🔹 Generate Data for Donut Chart
      setFilteredData(
        filtered.map((task) => ({
          name: task.taskTitle || "Untitled Task",
          value: task.totalHours || 0,
          color: "#6FCF97", // Default color
        }))
      );
    } else {
      setFilteredTasks([]);
      setTaskTotalHours(0);
      setFilteredData([]);
    }
  }, [selectedRange, selectedProduct]); // Runs when range or product changes

  useEffect(() => {
    if (selectedProduct?.taskArr) {
      const filtered = selectedProduct.taskArr.filter((task) =>
        isWithinRange(task.endTime, selectedRange.startDate, selectedRange.endDate)
      );

      setFilteredTasks(filtered);

      // 🔹 Calculate Total Hours
      const totalHours = filtered.reduce((sum, task) => sum + (task.totalHours || 0), 0);
      setTaskTotalHours(totalHours);

      // 🔹 Generate User Task Data
      const userTaskMap = {};

      filtered.forEach((task) => {
        task.assignee.forEach((userId) => {  // ✅ Iterate over all assignees
          const userName = getUserName(userId); // ✅ Fetch username

          if (!userTaskMap[userId]) {
            userTaskMap[userId] = {
              name: userName || `User ${userId}`, // ✅ Ensure name fallback
              totalSpent: 0
            };
          }

          userTaskMap[userId].totalSpent += task.totalHours || 0;
        });
      });

      console.log("Updated User Task Map:", userTaskMap);
      console.log("User Tasks Data:", Object.values(userTaskMap));

      // 🔹 Update State
      setUserTasks(Object.values(userTaskMap));
      setTotalSpent(totalHours);
    } else {
      setFilteredTasks([]);
      setTaskTotalHours(0);
      setUserTasks([]);
      setTotalSpent(0);
    }
  }, [selectedRange, selectedProduct]);

  useEffect(() => {
    if (!selectedProduct?.taskArr) { return; }

    // 🔹 Count "Completed", "Ongoing", and "To Do" tasks separately
    const taskCounts = selectedProduct.taskArr.reduce(
      (acc, task) => {
        if (task.taskStatus === "Completed") { acc.completed++; }
        else if (task.taskStatus === "In Progress") { acc.ongoing++; }
        else if (task.taskStatus === "To Do") { acc.todo++; } // Fix: Count "To Do" separately
        return acc;
      },
      { completed: 0, ongoing: 0, todo: 0 }
    );

    // 🔹 Update Task Overview Data
    const newTaskOverviewData = [
      { name: "Completed", value: taskCounts.completed, color: "#74C365" },
      { name: "Ongoing", value: taskCounts.ongoing, color: "#1E3A8A" },
      { name: "To Do", value: taskCounts.todo, color: "#FFA500" }, // Fix: Separate "To Do"
    ];

    setTaskOverviewData(newTaskOverviewData);
  }, [selectedProduct]);

  // Function to format date safely
  const formatDate = (date) => {
    if (!date) { return null; } // Prevent null/undefined issues
    const parsedDate = typeof date === "string" ? parseISO(date) : date;
    return isValid(parsedDate) ? format(parsedDate, "dd-MM-yyyy") : null;
  };

  // Function to dynamically generate date ranges
  const generateDateRange = (startDate, endDate) => {
    return eachDayOfInterval({ start: new Date(startDate), end: new Date(endDate) }).map(date => format(date, "dd-MM-yyyy"));
  };

  // Get the range (either custom or last 7 days)
  const billableselectedDates = generateDateRange(billableTaskRange.startDate, billableTaskRange.endDate);

  // 🔹 Process tasks into a map for quick lookup
  const billableTaskDataMap = filteredTasks.reduce((acc, task) => {
    if (!task.updatedAt) { return acc; }

    const formattedDate = formatDate(task.updatedAt);
    if (!acc[formattedDate]) {
      acc[formattedDate] = { date: formattedDate, taskHours: 0, billableHours: 0 };
    }

    if (task.billingStatus === "Billable") {
      acc[formattedDate].billableHours += task.totalHours;
    } else {
      acc[formattedDate].taskHours += task.totalHours;
    }

    return acc;
  }, {});

  // 🔹 Ensure all selected range dates exist
  const billableTaskData = billableselectedDates.map(date => ({
    date,
    taskHours: billableTaskDataMap[date]?.taskHours || 0,
    billableHours: billableTaskDataMap[date]?.billableHours || 0,
  }));

  const getOngoingTasks = (selectedProduct) => {
    if (!selectedProduct?.taskArr) { return []; }

    // Filter ongoing tasks
    return selectedProduct.taskArr.filter(task => task.taskStatus === "In Progress");
  };

  // Example usage:
  const ongoingTasks = getOngoingTasks(selectedProduct);
  console.log(ongoingTasks);

  const today = dayjs();
  // Filter products with future deadlines
  const upcomingProjects = products.filter((product) =>
    dayjs(product.endDate).isAfter(today)
  );

  const totalHours = selectedProduct?.taskArr ? selectedProduct.taskArr.reduce((sum, task) => {
    return sum + Object.values(task.hours || {}).reduce((taskSum, val) => taskSum + val, 0);
  }, 0) : 0; // Return 0 if selectedProduct or taskArr is null/undefined


  return (
    <Box sx={{ p: 3 }}>
      <Stack direction="row" spacing={2} alignItems="center" mb={2}>
        <Typography variant="h5" sx={{ fontWeight: "bold" }}>
          Product Overview
        </Typography>

        <FormControl sx={{ minWidth: 200 }}>
          <Select
            value={selectedProductId || ""}
            onChange={(e) => setSelectedProductId(e.target.value)}
            displayEmpty
          >
            <MenuItem value="" disabled>
              Select a product
            </MenuItem>
            {products.map((product) => (
              <MenuItem key={product._id} value={product._id}>
                {product.productName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Stack>

      {selectedProduct && (
        <>
          <Grid container spacing={3}>

            {/* Overview Cards */}

            {/* Total Hours */}
            <Grid item xs={12} sm={6} md={3}>
              <OverviewCard
                title="Total Hours"
                subtitle1="Total Hours"
                value1={`${calculateTotalProductHours(selectedProduct)} hr`}
                icon={<AccessTime fontSize="large" color="primary" />}
              />
            </Grid>

            {/* Billable & Non-Billable Hours */}
            <Grid item xs={12} sm={6} md={3}>
              <OverviewCard
                title="Billable Hour"
                subtitle1="Billable Hour"
                value1={
                  selectedProduct?.taskArr?.length ? `${selectedProduct.taskArr.
                    filter((task) => task.billingStatus === "Billable").
                    reduce(
                      (total, task) =>
                        total +
                        Object.values(task.hours || {}).reduce(
                          (sum, h) => sum + h,
                          0
                        ),
                      0
                    ).
                    toFixed(2)} hr` : "00 hr"
                }
                subtitle2="Non-Billable Hour"
                value2={
                  selectedProduct?.taskArr?.length ? `${selectedProduct.taskArr.
                    filter((task) => task.billingStatus === "Non-Billable").
                    reduce(
                      (total, task) =>
                        total +
                        Object.values(task.hours || {}).reduce(
                          (sum, h) => sum + h,
                          0
                        ),
                      0
                    ).
                    toFixed(2)} hr` : "--:--"
                }
                icon={<WorkOutline fontSize="large" color="primary" />}
              />
            </Grid>

            {/* Total Projects */}
            <Grid item xs={12} sm={6} md={3}>
              <OverviewCard
                title="Total Projects"
                subtitle1="Completed"
                value1={selectedProduct?.status === "Completed" ? "1" : "0"}
                subtitle2="Active"
                value2={selectedProduct?.status !== "Completed" ? "1" : "0"}
                icon={<Assignment fontSize="large" color="primary" />}
              />
            </Grid>

            {/* Tasks */}
            <Grid item xs={12} sm={6} md={3}>
              <OverviewCard
                title="Tasks"
                subtitle1="Total"
                value1={selectedProduct?.taskArr.length ?? "--:--"}
                subtitle2="Completed"
                value2={
                  selectedProduct?.taskArr.filter(
                    (task) => task.taskStatus === "Completed"
                  ).length ?? "--:--"
                }
                icon={<PlaylistAddCheck fontSize="large" color="primary" />}
              />
            </Grid>

            {/* Project Overview Card */}
            <Grid item xs={12} md={3}>
              <Card sx={{ minHeight: "100%", position: "relative", p: 2 }}>
                <CardContent sx={{ display: "flex", flexDirection: "column", alignItems: "center", height: "100%" }}>

                  {/* Title */}
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
                    Project Overview
                  </Typography>

                  {/* Donut Chart */}
                  <Box sx={{ position: "relative", display: "flex", justifyContent: "center", alignItems: "center", mb: 2 }}>
                    <ResponsiveContainer width={180} height={180}>
                      <PieChart>
                        <Pie
                          data={data}
                          cx="50%"
                          cy="50%"
                          innerRadius={65}
                          outerRadius={80}
                          fill="#8884d8"
                          paddingAngle={3}
                          dataKey="value"
                          stroke="none"
                        >
                          {data.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>

                    {/* Centered Total Projects Text */}
                    <Box sx={{ position: "absolute", top: "50%", left: "50%", transform: "translate(-50%, -50%)", textAlign: "center" }}>
                      <Typography variant="h5" fontWeight="bold" sx={{ color: "#2F80ED" }}>
                        {totalProjects}
                      </Typography>
                      <Typography variant="body2" sx={{ color: "gray" }}>
                        Projects
                      </Typography>
                    </Box>
                  </Box>

                  {/* Legend */}
                  <Grid container justifyContent="center" spacing={1}>
                    <Grid item sx={{ display: "flex", alignItems: "center" }}>
                      <Box sx={{ width: 10, height: 10, backgroundColor: "#6FCF97", borderRadius: "50%", mr: 0.5 }} />
                      <Typography variant="body2" sx={{ fontSize: "12px", color: "black" }}>Completed</Typography>
                      <Typography variant="body2" fontWeight="bold" sx={{ fontSize: "12px", ml: 0.5 }}>{completedProjects}</Typography>
                    </Grid>
                    <Grid item sx={{ display: "flex", alignItems: "center" }}>
                      <Box sx={{ width: 10, height: 10, backgroundColor: "#2F80ED", borderRadius: "50%", mr: 0.5 }} />
                      <Typography variant="body2" sx={{ fontSize: "12px", color: "black" }}>Ongoing</Typography>
                      <Typography variant="body2" fontWeight="bold" sx={{ fontSize: "12px", ml: 0.5 }}>{ongoingProjects}</Typography>
                    </Grid>
                  </Grid>

                </CardContent>
              </Card>

            </Grid>

            {/* Upcoming Project Deadline */}
            <Grid item xs={12} md={9}>
              <Card sx={{ minHeight: "100%", display: "flex", flexDirection: "column" }}>
                <CardContent sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
                  <Typography variant="h6">Upcoming Project Deadline</Typography>
                  {upcomingProjects.length > 0 ? (
                    <Grid container spacing={2} sx={{ marginTop: 1 }}>
                      {upcomingProjects.map((product) => (
                        <Grid item xs={12} key={product.id}>
                          <Typography variant="body1">
                            {product.productName} - {dayjs(product.endDate).format("DD MMM YYYY")}
                          </Typography>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Grid
                      container
                      justifyContent="center"
                      alignItems="center"
                      sx={{ flex: 1, display: "flex", flexDirection: "column" }}
                    >
                      <Search fontSize="large" color="disabled" sx={{ marginBottom: 1 }} />
                      <Typography variant="body2" color="textSecondary">No Upcoming Deadlines</Typography>
                    </Grid>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Task Section */}
            <Grid Grid item xs={12} md={6}>
              <Card sx={{ minHeight: "100%", p: 3, borderRadius: 3, boxShadow: 2, backgroundColor: "background.paper" }}>
                {/* Header */}
                <CardContent sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", pb: 1 }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: "text.primary" }}>
                    Tasks
                  </Typography>
                  <DateRangeSelector onRangeChange={setSelectedRange} />
                </CardContent>

                {filteredTasks.length > 0 ? (
                  <>
                    {/* Task Breakdown */}
                    <CardContent sx={{ display: "flex", alignItems: "center", gap: 3 }}>
                      {/* Donut Chart */}
                      <Box sx={{ position: "relative", display: "flex", justifyContent: "center", alignItems: "center", flex: 1 }}>
                        <ResponsiveContainer width={180} height={180}>
                          <PieChart>
                            <Pie data={filteredData} cx="50%" cy="50%" innerRadius={65} outerRadius={80} paddingAngle={3} dataKey="value" stroke="none">
                              {filteredData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                          </PieChart>
                        </ResponsiveContainer>

                        {/* Centered Total Hours */}
                        <Box sx={{ position: "absolute", top: "50%", left: "50%", transform: "translate(-50%, -50%)", textAlign: "center" }}>
                          <Typography variant="body2" sx={{ color: "gray" }}>Total</Typography>
                          <Typography variant="h6" fontWeight="bold" sx={{ color: "primary.main", fontSize: "16px" }}>
                            {formatHours(taskTotalHours)}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Task List */}
                      <Box sx={{ flex: 1 }}>
                        {filteredTasks.map((task, index) => (
                          <Box
                            key={index}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              mb: 1,
                              p: 1,
                              borderRadius: 2,
                              bgcolor: "action.hover",
                            }}
                          >
                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                              <Box sx={{ width: 8, height: 8, borderRadius: "50%", bgcolor: task.color || "#6FCF97" }} />
                              <Typography variant="body2" sx={{ color: "text.primary" }}>
                                {typeof task.taskTitle === "object" ? JSON.stringify(task.taskTitle) : task.taskTitle}
                              </Typography>
                            </Box>
                            <Typography variant="body2" fontWeight="bold" sx={{ color: "text.secondary" }}>
                              {formatHours(task.totalHours)}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    </CardContent>

                    {/* User & Total Spent Table */}
                    <CardContent sx={{ mt: 2 }}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell sx={{ fontWeight: "bold", color: "text.primary" }}>User</TableCell>
                            <TableCell sx={{ fontWeight: "bold", color: "text.primary" }}>Task Spent</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {userTasks.filter(user => user.name && user.totalSpent > 0).map((user, index) => (
                            <TableRow key={index}>
                              <TableCell sx={{ width: "50%", textAlign: "left" }}>
                                <Typography variant="body2" sx={{ color: "text.primary" }}>{user.name}</Typography>
                              </TableCell>
                              <TableCell sx={{ width: "50%", textAlign: "left" }}>
                                <Typography variant="body2" fontWeight="bold" sx={{ color: "text.secondary" }}>
                                  {formatHours(user.totalSpent)}
                                </Typography>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>


                      </Table>
                    </CardContent>
                  </>
                ) : (
                  <CardContent sx={{ textAlign: "center", py: 3 }}>
                    <Typography variant="body2" sx={{ color: "gray" }}>No tasks found</Typography>
                  </CardContent>
                )}
              </Card>
            </Grid>

            {/* Billable Task Bar */}
            <Grid item xs={12} md={6}>
              <Card sx={{ minHeight: "100%", p: 3, borderRadius: 3, boxShadow: 2, backgroundColor: "background.paper" }}>
                <CardContent sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", pb: 1 }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: "text.primary", fontSize: 18 }}>
                    Billable Task Bar
                  </Typography>
                  <DateRangeSelector onRangeChange={setBillableTaskRange} />

                </CardContent>

                {/* Scrollable Container for Large Date Ranges */}
                <CardContent sx={{ overflowX: "auto", whiteSpace: "nowrap" }}>
                  {billableTaskData.length > 0 ? (
                    <ResponsiveContainer
                      width={billableTaskData.length > 7 ? billableTaskData.length * 80 : "100%"}
                      height={320}
                    >
                      <BarChart data={billableTaskData} barCategoryGap="30%" barGap={5}>
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />

                        <XAxis
                          dataKey="date"
                          tick={{ fontSize: 14, dy: 10 }}
                          height={50}
                          tickLine={false}
                          interval={0}  // Ensures all dates appear
                          padding={{ left: 35, right: 35 }}
                          scale="point"
                        />
                        <YAxis
                          domain={[0, (dataMax) => Math.max(9, Math.ceil(dataMax) + 2)]}
                          tickCount={10}
                          tickFormatter={(value) => formatHours(value)}
                        />


                        <Tooltip formatter={(value) => (value > 0 ? formatHours(value) : null)} cursor={{ fill: "transparent" }} />
                        <Legend />

                        {/* Ensure Task bar appears unless taskHours is explicitly 0 */}
                        {billableTaskData.some((d) => d.taskHours > 0) && (
                          <Bar
                            dataKey="taskHours"
                            fill="#1E3A8A"
                            name="Task"
                            barSize={25}
                            isAnimationActive={false}
                          />
                        )}

                        <Bar
                          dataKey="billableHours"
                          fill="#74C365"
                          name="Billable"
                          barSize={25}
                          isAnimationActive={false}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <Typography sx={{ textAlign: "center", color: "text.secondary", fontSize: 14 }}>
                      No data available for the selected date range.
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Task Overview Section */}
            <Grid item xs={12} md={6}>
              <Card sx={{ minHeight: "100%", p: 3, borderRadius: 3, boxShadow: 2, backgroundColor: "background.paper" }}>
                {/* Title */}
                <CardContent sx={{ pb: 1 }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: "text.primary", fontSize: 18 }}>
                    Task Overview
                  </Typography>
                </CardContent>

                <CardContent sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                  {/* Donut Chart with Centered Total */}
                  <Box sx={{ position: "relative", width: "40%", height: 150 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={taskOverviewData}
                          dataKey="value"
                          nameKey="name"
                          innerRadius="70%"
                          outerRadius="90%"
                          fill="#74C365"
                          labelLine={false}
                        >
                          {taskOverviewData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>

                    {/* Centered Total Task Count */}
                    <Box
                      sx={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        textAlign: "center",
                      }}
                    >
                      <Typography variant="h4" fontWeight="bold" sx={{ color: "#74C365" }}>
                        {taskOverviewData.reduce((sum, task) => sum + task.value, 0)}
                      </Typography>
                      <Typography variant="body2" sx={{ color: "text.secondary" }}>
                        Tasks
                      </Typography>
                    </Box>
                  </Box>

                  {/* Task Details */}
                  <Box sx={{ ml: 3 }}>
                    {taskOverviewData.map((task, index) => (
                      <Box key={index} sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                        <Box sx={{ width: 10, height: 10, borderRadius: "50%", backgroundColor: task.color, mr: 1 }} />
                        <Typography variant="body2" sx={{ color: "text.primary", fontWeight: "bold" }}>
                          {task.name}
                        </Typography>
                        <Typography variant="body2" sx={{ color: "text.secondary", ml: 1, fontWeight: "bold" }}>
                          {task.value}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Ongoing Task List */}
            <Grid item xs={12} md={6}>
              <Card sx={{ minHeight: "100%", p: 3, borderRadius: 3, boxShadow: 2, backgroundColor: "background.paper" }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" sx={{ color: "text.primary", fontSize: 18 }}>
                    Ongoing Tasks
                  </Typography>
                </CardContent>

                <CardContent>
                  {ongoingTasks.length > 0 ? (
                    <List>
                      {ongoingTasks.map((task) => (
                        <ListItem key={task._id} sx={{ borderBottom: "1px solid #ddd" }}>
                          <ListItemText
                            primary={task.taskTitle}

                            secondary={`Priority: ${task.priority} | Hours: ${formatHours(totalHours)}`}

                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", mt: 3 }}>
                      <Search />
                      <Typography variant="body2" sx={{ color: "text.secondary" }}>No Ongoing Tasks</Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default ProductOverview;
