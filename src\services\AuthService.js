import axios from "axios";
import { API_BASE_URL } from "../utils/apiConfig";

/**
 * Login function with improved error handling
 * @param {Object} params - Login credentials
 * @returns {Promise} - Axios response promise
 */
const Login = async (params) => {
  try {
    // The login endpoint is at /api/login
    // For Render deployment, we need to use the base URL without any modifications
    const baseUrl = API_BASE_URL.endsWith('/api') ? API_BASE_URL.substring(0, API_BASE_URL.length - 4) : API_BASE_URL; // Remove /api if it exists, otherwise use as is

    const loginUrl = `${baseUrl}/api/login`;
    console.log('Attempting login with API URL:', loginUrl);

    // Set up request configuration with CORS headers
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      withCredentials: false // Set to true if your API requires credentials
    };

    // Make the request
    const response = await axios.post(loginUrl, params, config);
    console.log('Login response:', response);
    return response;
  } catch (error) {
    console.error('Login error:', error);

    // Provide more detailed error information
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Error request:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }

    // Rethrow the error for the caller to handle
    throw error;
  }
};

export const AuthService = {
  Login
};
