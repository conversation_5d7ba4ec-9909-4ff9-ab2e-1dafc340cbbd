/**
 * EMS Types Index
 * 
 * This file exports all TypeScript type definitions for the Employee Management System module.
 * Following strict typing conventions for better code quality and IDE support.
 * 
 * Usage:
 * import { Employee, Department, Designation } from 'modules/ems/types';
 */

// Base Types
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

// Employee/User Types
export interface Employee extends BaseEntity {
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: Gender;
  maritalStatus?: MaritalStatus;
  nationality?: string;
  address?: Address;
  emergencyContacts?: EmergencyContact[];
  
  // Employment Details
  departmentId: string;
  designationId: string;
  managerId?: string;
  employeeType: EmployeeType;
  employmentStatus: EmploymentStatus;
  joiningDate: string;
  probationEndDate?: string;
  confirmationDate?: string;
  terminationDate?: string;
  
  // Work Details
  workLocation?: WorkLocation;
  workScheduleId?: string;
  reportingManagerId?: string;
  
  // System Details
  userId?: string;
  profilePicture?: string;
  isActive: boolean;
  permissions?: Permission[];
  roles?: Role[];
  
  // Additional Info
  skills?: string[];
  certifications?: Certification[];
  documents?: EmployeeDocument[];
}

export interface User extends BaseEntity {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  isActive: boolean;
  lastLoginAt?: string;
  employeeId?: string;
  roles: Role[];
  permissions: Permission[];
  preferences?: UserPreferences;
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other'
}

export enum MaritalStatus {
  SINGLE = 'single',
  MARRIED = 'married',
  DIVORCED = 'divorced',
  WIDOWED = 'widowed'
}

export enum EmployeeType {
  PERMANENT = 'permanent',
  CONTRACT = 'contract',
  INTERN = 'intern',
  CONSULTANT = 'consultant',
  PART_TIME = 'part_time'
}

export enum EmploymentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PROBATION = 'probation',
  NOTICE_PERIOD = 'notice_period',
  TERMINATED = 'terminated',
  RESIGNED = 'resigned'
}

export enum WorkLocation {
  OFFICE = 'office',
  REMOTE = 'remote',
  HYBRID = 'hybrid',
  CLIENT_SITE = 'client_site'
}

// Address Types
export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  type: AddressType;
}

export enum AddressType {
  PERMANENT = 'permanent',
  CURRENT = 'current',
  TEMPORARY = 'temporary'
}

// Emergency Contact Types
export interface EmergencyContact extends BaseEntity {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  address?: string;
  isPrimary: boolean;
}

// Department Types
export interface Department extends BaseEntity {
  name: string;
  code: string;
  description?: string;
  parentDepartmentId?: string;
  managerId?: string;
  location?: string;
  budget?: number;
  isActive: boolean;
  employeeCount?: number;
  subDepartments?: Department[];
}

// Designation Types
export interface Designation extends BaseEntity {
  title: string;
  code: string;
  description?: string;
  departmentId?: string;
  level: number;
  parentDesignationId?: string;
  salaryRange?: SalaryRange;
  responsibilities?: string[];
  requirements?: string[];
  isActive: boolean;
  employeeCount?: number;
}

export interface SalaryRange {
  min: number;
  max: number;
  currency: string;
}

// Role and Permission Types
export interface Role extends BaseEntity {
  name: string;
  code: string;
  description?: string;
  permissions: Permission[];
  isActive: boolean;
}

export interface Permission extends BaseEntity {
  name: string;
  code: string;
  resource: string;
  action: string;
  description?: string;
}

// Profile Types
export interface Profile extends BaseEntity {
  employeeId: string;
  bio?: string;
  profilePicture?: string;
  socialLinks?: SocialLink[];
  preferences?: ProfilePreferences;
  completeness: number;
}

export interface SocialLink {
  platform: string;
  url: string;
}

export interface ProfilePreferences {
  theme: 'light' | 'dark';
  language: string;
  timezone: string;
  notifications: NotificationPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  notifications: NotificationPreferences;
}

// Certification Types
export interface Certification extends BaseEntity {
  name: string;
  issuingOrganization: string;
  issueDate: string;
  expiryDate?: string;
  credentialId?: string;
  credentialUrl?: string;
  isVerified: boolean;
}

// Document Types
export interface EmployeeDocument extends BaseEntity {
  name: string;
  type: DocumentType;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: string;
  isVerified: boolean;
  expiryDate?: string;
}

export enum DocumentType {
  RESUME = 'resume',
  ID_PROOF = 'id_proof',
  ADDRESS_PROOF = 'address_proof',
  EDUCATION_CERTIFICATE = 'education_certificate',
  EXPERIENCE_LETTER = 'experience_letter',
  OFFER_LETTER = 'offer_letter',
  CONTRACT = 'contract',
  OTHER = 'other'
}

// Onboarding Types
export interface OnboardingTask extends BaseEntity {
  title: string;
  description?: string;
  type: OnboardingTaskType;
  assignedTo: string;
  dueDate?: string;
  status: OnboardingTaskStatus;
  completedAt?: string;
  completedBy?: string;
  notes?: string;
  dependencies?: string[];
  order: number;
}

export enum OnboardingTaskType {
  DOCUMENTATION = 'documentation',
  SYSTEM_ACCESS = 'system_access',
  TRAINING = 'training',
  ORIENTATION = 'orientation',
  EQUIPMENT = 'equipment',
  INTRODUCTION = 'introduction',
  POLICY_REVIEW = 'policy_review'
}

export enum OnboardingTaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  OVERDUE = 'overdue'
}

export interface OnboardingChecklist extends BaseEntity {
  employeeId: string;
  templateId?: string;
  status: OnboardingStatus;
  startDate: string;
  expectedCompletionDate: string;
  actualCompletionDate?: string;
  tasks: OnboardingTask[];
  progress: number;
  assignedTo: string;
}

export enum OnboardingStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ON_HOLD = 'on_hold',
  CANCELLED = 'cancelled'
}

// Organization Chart Types
export interface OrganizationNode {
  id: string;
  employeeId: string;
  name: string;
  designation: string;
  department: string;
  profilePicture?: string;
  managerId?: string;
  children?: OrganizationNode[];
  level: number;
}

// Filter Types
export interface EmployeeFilters {
  departmentId?: string;
  designationId?: string;
  employeeType?: EmployeeType[];
  employmentStatus?: EmploymentStatus[];
  workLocation?: WorkLocation[];
  managerId?: string;
  joiningDateRange?: DateRange;
  search?: string;
}

export interface DepartmentFilters {
  parentDepartmentId?: string;
  managerId?: string;
  isActive?: boolean;
  search?: string;
}

export interface DesignationFilters {
  departmentId?: string;
  level?: number;
  parentDesignationId?: string;
  isActive?: boolean;
  search?: string;
}

export interface DateRange {
  startDate: string;
  endDate: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface EmployeeFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: Gender;
  departmentId: string;
  designationId: string;
  employeeType: EmployeeType;
  joiningDate: string;
  managerId?: string;
  workLocation?: WorkLocation;
  address?: Address;
  emergencyContacts?: EmergencyContact[];
}

export interface DepartmentFormData {
  name: string;
  code: string;
  description?: string;
  parentDepartmentId?: string;
  managerId?: string;
  location?: string;
  budget?: number;
}

export interface DesignationFormData {
  title: string;
  code: string;
  description?: string;
  departmentId?: string;
  level: number;
  parentDesignationId?: string;
  salaryRange?: SalaryRange;
  responsibilities?: string[];
  requirements?: string[];
}

// Metrics Types
export interface EmployeeMetrics {
  totalEmployees: number;
  activeEmployees: number;
  newHires: number;
  terminations: number;
  employeesByDepartment: DepartmentMetric[];
  employeesByDesignation: DesignationMetric[];
  employeesByType: EmployeeTypeMetric[];
  averageTenure: number;
  turnoverRate: number;
}

export interface DepartmentMetric {
  departmentId: string;
  departmentName: string;
  employeeCount: number;
  percentage: number;
}

export interface DesignationMetric {
  designationId: string;
  designationTitle: string;
  employeeCount: number;
  percentage: number;
}

export interface EmployeeTypeMetric {
  type: EmployeeType;
  count: number;
  percentage: number;
}

// TODO: Add more specific types as needed during migration
// TODO: Implement proper validation schemas using Yup or Zod
// TODO: Add JSDoc comments for better documentation
// TODO: Consider using branded types for IDs to prevent mixing different entity IDs
// TODO: Add proper error types for better error handling
