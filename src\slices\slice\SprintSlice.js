import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    sprints: [],
    sprintDetails: null,
    loading: false,
    error: null,
    pagination: {
        currentPage: 1,
        totalPages: 1,
        totalRecords: 0,
        perPage: 10
    }
};

const SprintSlice = createSlice({
    name: "sprint",
    initialState,
    reducers: {
        // Create a new sprint
        createSprint: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Update an existing sprint
        updateSprint: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Delete a sprint
        deleteSprint: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Get all sprints
        getSprints: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Get a sprint by ID
        getSprintById: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Add a task to a sprint
        addTaskToSprint: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Remove a task from a sprint
        removeTaskFromSprint: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Start a sprint
        startSprint: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Complete a sprint
        completeSprint: (state) => {
            state.loading = true;
            state.error = null;
        },
        
        // Success handlers
        getSuccessfullySprints: (state, action) => {
            state.loading = false;
            state.sprints = action.payload.data;
            if (action.payload.pagination) {
                state.pagination = action.payload.pagination;
            }
        },
        
        getSuccessfullySprintById: (state, action) => {
            state.loading = false;
            state.sprintDetails = action.payload.data;
        },
        
        // Error handler
        getErrorSprints: (state, action) => {
            state.loading = false;
            state.error = action.payload;
        }
    }
});

export default SprintSlice;