import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { SprintActions } from '../../../slices/actions';
import SprintList from '../components/SprintList';
import { getSprints, isSprintLoading } from '../../../selectors/SprintSelector';
import PageTitle from '../../../components/PageTitle';
import SprintForm from '../components/SprintForm';
import Can from '../../../utils/can';
import { actions, features } from '../../../constants/permission';

/**
 * User Sprint Page
 *
 * This component provides a UI for users to view their own sprints.
 */
const UserSprintPage = () => {
  const dispatch = useDispatch();
  const sprints = useSelector(getSprints);
  const loading = useSelector(isSprintLoading);
  const [openForm, setOpenForm] = useState(false);

    // Add form handlers
  const handleOpenForm = () => setOpenForm(true);
  const handleCloseForm = () => setOpenForm(false);

    const handleCreateSprint = (sprintData) => {
    dispatch(SprintActions.createSprint({
      ...sprintData,
      createdBy: currentUser?._id
    }));
    handleCloseForm();
  };

  // Get current user from Redux store
  const currentUser = useSelector(state => state.user.profile);

  // Permission-based access check
  const canCreateSprint = Can(actions.create, features.sprint);
  console.log('Can Create Sprint:', canCreateSprint, '| User:', currentUser);

  // Fetch user's sprints when component mounts
  useEffect(() => {
    if (currentUser?._id) {
      dispatch(SprintActions.getSprints({ userId: currentUser._id }));
    }
  }, [dispatch, currentUser]);

  const handleEditSprint = (sprint) => {
    window.location.href = `/app/sprint/form/${sprint.id}`;
  };

  const handleDeleteSprint = () => { };
  const handleStartSprint = () => { };
  const handleCompleteSprint = () => { };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <PageTitle title="My Sprints" />

      
    <Paper sx={{ p: 2, mb: 2 }}>
  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
    <Box>
      <Typography variant="h6">
        Sprints I&apos;m Involved In
      </Typography>
      <Typography variant="body2" color="textSecondary">
        View sprints you&apos;ve created or are assigned
      </Typography>
    </Box>

  {canCreateSprint && (
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handleOpenForm}
          sx={{ whiteSpace: 'nowrap' }}
        >
          + New Sprint
        </Button>
      )}

            {/* Add the SprintForm dialog */}
      <Dialog open={openForm} onClose={handleCloseForm} maxWidth="md" fullWidth>
        <DialogTitle>Create New Sprint</DialogTitle>
        <DialogContent>
          <SprintForm onSubmit={handleCreateSprint} />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseForm} color="secondary">
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

  </Box>
</Paper>


        <SprintList
          sprints={sprints}
          loading={loading}
          onEdit={handleEditSprint}
          onDelete={handleDeleteSprint}
          onStart={handleStartSprint}
          onComplete={handleCompleteSprint}
          isAdmin={false}
          currentUserId={currentUser?._id}
          readOnly={true}
        />
      </Box>
    </Container>
  );
};

export default UserSprintPage;
