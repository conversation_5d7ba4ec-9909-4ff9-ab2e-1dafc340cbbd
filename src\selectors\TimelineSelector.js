import {createSelector} from "@reduxjs/toolkit";


const timelineSelector = (state) => state.timeline;

const getTimelineRequests = () => createSelector(
    timelineSelector,
    timeline => timeline.timelineArr
);

const getPagination = () => createSelector(
    timelineSelector,
    timeline => timeline.pagination
)
 

const getTimelineRequestsToday = () => createSelector(
    timelineSelector,
    timeline => timeline.timelineToday

    
)

export const TimelineSelector = {
    getTimelineRequests,
    getPagination,
    getTimelineRequestsToday
}