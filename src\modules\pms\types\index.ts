/**
 * PMS Types Index
 * 
 * This file exports all TypeScript type definitions for the Project Management System module.
 * Following strict typing conventions for better code quality and IDE support.
 * 
 * Usage:
 * import { Project, Task, Sprint } from 'modules/pms/types';
 */

// Base Types
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

// Project Types
export interface Project extends BaseEntity {
  name: string;
  description?: string;
  status: ProjectStatus;
  priority: ProjectPriority;
  startDate: string;
  endDate?: string;
  clientId: string;
  managerId: string;
  teamMembers: string[];
  budget?: number;
  progress: number;
  tags?: string[];
}

export enum ProjectStatus {
  PLANNING = 'planning',
  IN_PROGRESS = 'in_progress',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Task Types
export interface Task extends BaseEntity {
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  projectId: string;
  assigneeId?: string;
  reporterId: string;
  sprintId?: string;
  estimatedHours?: number;
  actualHours?: number;
  dueDate?: string;
  tags?: string[];
  dependencies?: string[];
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  IN_REVIEW = 'in_review',
  DONE = 'done',
  BLOCKED = 'blocked'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// Sprint Types
export interface Sprint extends BaseEntity {
  name: string;
  description?: string;
  projectId: string;
  status: SprintStatus;
  startDate: string;
  endDate: string;
  goal?: string;
  capacity: number;
  velocity?: number;
  taskIds: string[];
}

export enum SprintStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// Client Types
export interface Client extends BaseEntity {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  address?: Address;
  status: ClientStatus;
  projects: string[];
}

export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
}

export enum ClientStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PROSPECT = 'prospect'
}

// Timeline Types
export interface TimelineEvent extends BaseEntity {
  title: string;
  description?: string;
  type: TimelineEventType;
  entityId: string;
  entityType: TimelineEntityType;
  userId: string;
  metadata?: Record<string, any>;
}

export enum TimelineEventType {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  STATUS_CHANGED = 'status_changed',
  ASSIGNED = 'assigned',
  COMMENTED = 'commented'
}

export enum TimelineEntityType {
  PROJECT = 'project',
  TASK = 'task',
  SPRINT = 'sprint',
  CLIENT = 'client'
}

// Timesheet Types
export interface TimesheetEntry extends BaseEntity {
  userId: string;
  projectId: string;
  taskId?: string;
  date: string;
  hours: number;
  description?: string;
  status: TimesheetStatus;
}

export enum TimesheetStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

// Filter Types
export interface ProjectFilters {
  status?: ProjectStatus[];
  priority?: ProjectPriority[];
  clientId?: string;
  managerId?: string;
  dateRange?: DateRange;
  search?: string;
}

export interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  projectId?: string;
  assigneeId?: string;
  sprintId?: string;
  dateRange?: DateRange;
  search?: string;
}

export interface DateRange {
  startDate: string;
  endDate: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface ProjectFormData {
  name: string;
  description?: string;
  clientId: string;
  managerId: string;
  startDate: string;
  endDate?: string;
  budget?: number;
  priority: ProjectPriority;
  teamMembers: string[];
  tags?: string[];
}

export interface TaskFormData {
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  priority: TaskPriority;
  estimatedHours?: number;
  dueDate?: string;
  tags?: string[];
}

// TODO: Add more specific types as needed during migration
// TODO: Implement proper validation schemas using Yup or Zod
// TODO: Add JSDoc comments for better documentation
// TODO: Consider using branded types for IDs to prevent mixing different entity IDs
